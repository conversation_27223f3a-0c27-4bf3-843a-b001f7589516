"use client"
import { useEffect, useRef, useState } from 'react';

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  life: number;
}

/**
 * Predefined particle configurations to avoid hydration errors
 * These values create a natural, distributed particle system
 */
const particleSeeds = [
  { x: 0.15, y: 0.23, vx: 0.1, vy: -0.05, size: 1.2, opacity: 0.3 },
  { x: 0.67, y: 0.45, vx: -0.15, vy: 0.08, size: 2.1, opacity: 0.5 },
  { x: 0.34, y: 0.78, vx: 0.08, vy: -0.12, size: 1.8, opacity: 0.4 },
  { x: 0.89, y: 0.12, vx: -0.1, vy: 0.15, size: 1.5, opacity: 0.6 },
  { x: 0.23, y: 0.56, vx: 0.12, vy: -0.08, size: 2.3, opacity: 0.3 },
  { x: 0.78, y: 0.89, vx: -0.08, vy: 0.1, size: 1.7, opacity: 0.5 },
  { x: 0.45, y: 0.34, vx: 0.15, vy: -0.1, size: 1.9, opacity: 0.4 },
  { x: 0.12, y: 0.67, vx: -0.12, vy: 0.12, size: 1.4, opacity: 0.6 },
  { x: 0.56, y: 0.91, vx: 0.1, vy: -0.15, size: 2.0, opacity: 0.3 },
  { x: 0.91, y: 0.28, vx: -0.15, vy: 0.08, size: 1.6, opacity: 0.5 },
  { x: 0.38, y: 0.15, vx: 0.08, vy: -0.1, size: 2.2, opacity: 0.4 },
  { x: 0.72, y: 0.73, vx: -0.1, vy: 0.15, size: 1.3, opacity: 0.6 },
  { x: 0.19, y: 0.42, vx: 0.12, vy: -0.08, size: 1.8, opacity: 0.3 },
  { x: 0.84, y: 0.58, vx: -0.08, vy: 0.1, size: 2.1, opacity: 0.5 },
  { x: 0.51, y: 0.85, vx: 0.15, vy: -0.12, size: 1.5, opacity: 0.4 }
];

/**
 * ParticleBackground component with hydration-safe implementation
 * Uses deterministic particle generation to avoid server/client mismatches
 * @returns {JSX.Element} Canvas-based particle background
 */
export default function ParticleBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>(0);
  const particlesRef = useRef<Particle[]>([]);
  const mouseRef = useRef({ x: 0, y: 0 });
  const [isMounted, setIsMounted] = useState(false);

  // Ensure component is mounted on client before running canvas operations
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    // Only run on client side after mounting
    if (!isMounted || typeof window === 'undefined') {
      return;
    }

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    /**
     * Create particle using deterministic seed values
     * @param {number} index - Seed index for deterministic generation
     * @returns {Particle} Generated particle with deterministic properties
     */
    const createParticle = (index: number): Particle => {
      const seed = particleSeeds[index % particleSeeds.length];
      return {
        x: seed.x * canvas.width,
        y: seed.y * canvas.height,
        vx: seed.vx,
        vy: seed.vy,
        size: seed.size,
        opacity: seed.opacity,
        life: 1
      };
    };

    const initParticles = () => {
      const particleCount = Math.min(15, Math.floor((canvas.width * canvas.height) / 15000));
      particlesRef.current = [];
      for (let i = 0; i < particleCount; i++) {
        particlesRef.current.push(createParticle(i));
      }
    };

    const updateParticles = () => {
      const particles = particlesRef.current;
      const mouse = mouseRef.current;

      particles.forEach((particle) => {
        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Mouse interaction
        const dx = mouse.x - particle.x;
        const dy = mouse.y - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 100) {
          const force = (100 - distance) / 100;
          particle.vx += (dx / distance) * force * 0.01;
          particle.vy += (dy / distance) * force * 0.01;
        }

        // Boundary checks
        if (particle.x < 0 || particle.x > canvas.width) {
          particle.vx *= -1;
        }
        if (particle.y < 0 || particle.y > canvas.height) {
          particle.vy *= -1;
        }

        // Keep particles in bounds
        particle.x = Math.max(0, Math.min(canvas.width, particle.x));
        particle.y = Math.max(0, Math.min(canvas.height, particle.y));
      });
    };

    const drawParticles = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      const particles = particlesRef.current;

      particles.forEach((particle, index) => {
        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(233, 69, 96, ${particle.opacity})`;
        ctx.fill();

        // Draw connections
        particles.slice(index + 1).forEach(otherParticle => {
          const dx = particle.x - otherParticle.x;
          const dy = particle.y - otherParticle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 100) {
            ctx.beginPath();
            ctx.moveTo(particle.x, particle.y);
            ctx.lineTo(otherParticle.x, otherParticle.y);
            ctx.strokeStyle = `rgba(233, 69, 96, ${0.1 * (1 - distance / 100)})`;
            ctx.lineWidth = 1;
            ctx.stroke();
          }
        });
      });
    };

    const animate = () => {
      updateParticles();
      drawParticles();
      animationRef.current = requestAnimationFrame(animate);
    };

    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current.x = e.clientX;
      mouseRef.current.y = e.clientY;
    };

    const handleResize = () => {
      resizeCanvas();
      initParticles();
    };

    // Initialize
    resizeCanvas();
    initParticles();
    animate();

    // Event listeners
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('resize', handleResize);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('resize', handleResize);
    };
  }, [isMounted]);

  // Prevent hydration mismatch by not rendering canvas until mounted
  if (!isMounted) {
    return null;
  }

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{ zIndex: 1 }}
    />
  );
}
