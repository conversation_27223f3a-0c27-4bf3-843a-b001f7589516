"use client";

import { useScrollAnimation } from "@/hooks/useScrollAnimation";

export default function AboutSection() {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section id="about" className="py-32 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-64 h-64 bg-gradient-radial from-primary/10 via-primary/5 to-transparent rounded-full blur-3xl animate-float-slow"></div>
        <div className="absolute bottom-20 right-10 w-80 h-80 bg-gradient-radial from-blue-500/10 via-purple-500/5 to-transparent rounded-full blur-3xl animate-float-medium"></div>
        <div className="absolute top-1/2 left-1/3 w-48 h-48 bg-gradient-radial from-purple-500/8 via-pink-500/4 to-transparent rounded-full blur-2xl animate-float-fast"></div>

        {/* Floating particles */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-primary/60 rounded-full animate-twinkle-slow"></div>
        <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-blue-400/60 rounded-full animate-twinkle-medium"></div>
        <div className="absolute top-1/2 right-1/3 w-1.5 h-1.5 bg-purple-400/60 rounded-full animate-twinkle-fast"></div>
        <div className="absolute bottom-1/3 left-1/6 w-1 h-1 bg-pink-400/60 rounded-full animate-twinkle-slow"></div>
      </div>

      <div className="max-w-6xl mx-auto px-8 relative z-10">
        <div
          ref={ref}
          className={`transition-all duration-1000 ${
            isVisible ? "animate-fade-in-up" : "opacity-0 translate-y-12"
          }`}
        >
          <h2 className="font-bold text-4xl md:text-5xl text-center mb-16 glow-text font-mono animate-glow-pulse">
            About <span className="text-primary animate-text-shimmer">Me</span>
          </h2>

          <div className="grid md:grid-cols-1 gap-16 items-center bg-gray-800/10 p-8 rounded-2xl backdrop-blur-sm border !border-gray-900/30 hover:border-primary/30 transition-all duration-500 animate-card-hover">
            <div>
              <p className="text-muted-foreground mb-6 leading-relaxed animate-fade-in-delayed opacity-0">
                Driven by a passion for building impactful web applications, I
                {"'"}m a versatile Full Stack Developer with a strong track
                record of translating complex business requirements into
                high-performance, scalable solutions. I thrive in dynamic
                environments, embrace challenges with enthusiasm, and commit to
                continuous learning.
              </p>
              <p className="text-muted-foreground mb-6 leading-relaxed animate-fade-in-delayed-2 opacity-0">
                My journey began in the PHP ecosystem, delivering high-quality
                Laravel and WordPress solutions, which provided a deep
                understanding of web architecture. I have since evolved into
                modern JavaScript frameworks, specializing in React, Vue.js,
                Next.js, Nuxt.js, and Node.js.
              </p>
              <p className="text-muted-foreground mb-6 leading-relaxed animate-fade-in-delayed-3 opacity-0">
                Today, my core expertise lies in:
                <br />- Frontend: React, Vue.js, Next.js, Nuxt.js, TypeScript
                <br />- Backend: Node.js, Express, Laravel, PHP
                <br />- Databases: MySQL, PlanetScale, MongoDB, Firebase,
                Supabase
                <br />- Dev Tools: Git, Docker, Vercel, Netlify
              </p>

              <p className="text-muted-foreground mb-6 leading-relaxed animate-fade-in-delayed-4 opacity-0">
                Beyond development, I specialize in marketing automation and AI
                chatbot development. My experience includes working directly
                with HighLevel (GoHighLevel) as a Full Stack Engineer, where I
                gained deep insights into marketing automation platforms and
                SaaS scalability. I{"'"}ve customized and extended platforms
                like ClickFunnels, HighLevel, and Kajabi, and architected
                complex automation workflows using Zapier, n8n, and Make.com.
              </p>

              <p className="text-muted-foreground mb-6 leading-relaxed animate-fade-in-delayed-5 opacity-0">
                I also build intelligent AI-powered chatbots to enhance user
                experience and deliver robust e-commerce solutions on Shopify
                and WooCommerce, with seamless integration into tools like
                GetResponse and Stripe.
              </p>

              <div className="flex flex-wrap gap-4 mt-8 animate-fade-in-delayed-6 opacity-0">
                <div className="glassmorphism px-6 py-3 rounded-full hover-glow animate-stats-card transform hover:scale-105 transition-all duration-300">
                  <i className="fas fa-code mr-2 text-primary animate-icon-pulse"></i>
                  15+ Years Experience
                </div>
                <div
                  className="glassmorphism px-6 py-3 rounded-full hover-glow animate-stats-card transform hover:scale-105 transition-all duration-300"
                  style={{ animationDelay: "0.2s" }}
                >
                  <i className="fas fa-star mr-2 text-primary animate-icon-pulse"></i>
                  4.9/5 Client Rating
                </div>
                <div
                  className="glassmorphism px-6 py-3 rounded-full hover-glow animate-stats-card transform hover:scale-105 transition-all duration-300"
                  style={{ animationDelay: "0.4s" }}
                >
                  <i className="fas fa-project-diagram mr-2 text-primary animate-icon-pulse"></i>
                  500+ Projects
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Custom CSS for AboutSection animations */}
      <style jsx>{`
        .animate-float-slow {
          animation: float 8s ease-in-out infinite;
        }

        .animate-float-medium {
          animation: float 6s ease-in-out infinite;
          animation-delay: 2s;
        }

        .animate-float-fast {
          animation: float 4s ease-in-out infinite;
          animation-delay: 1s;
        }

        .animate-twinkle-slow {
          animation: twinkle 3s ease-in-out infinite;
        }

        .animate-twinkle-medium {
          animation: twinkle 2.5s ease-in-out infinite;
          animation-delay: 1s;
        }

        .animate-twinkle-fast {
          animation: twinkle 2s ease-in-out infinite;
          animation-delay: 0.5s;
        }

        .animate-glow-pulse {
          animation: glowPulse 4s ease-in-out infinite;
        }

        .animate-text-shimmer {
          animation: textShimmer 3s ease-in-out infinite;
        }

        .animate-fade-in-delayed {
          animation: fadeInUp 0.8s ease-out 0.5s both;
        }

        .animate-fade-in-delayed-2 {
          animation: fadeInUp 0.8s ease-out 0.8s both;
        }

        .animate-fade-in-delayed-3 {
          animation: fadeInUp 0.8s ease-out 1.1s both;
        }

        .animate-fade-in-delayed-4 {
          animation: fadeInUp 0.8s ease-out 1.4s both;
        }

        .animate-fade-in-delayed-5 {
          animation: fadeInUp 0.8s ease-out 1.7s both;
        }

        .animate-fade-in-delayed-6 {
          animation: fadeInUp 0.8s ease-out 2s both;
        }

        .animate-card-hover {
          animation: cardFloat 4s ease-in-out infinite;
        }

        .animate-stats-card {
          animation: statsReveal 0.6s ease-out both;
        }

        .animate-icon-pulse {
          animation: iconPulse 2s ease-in-out infinite;
        }

        @keyframes float {
          0%,
          100% {
            transform: translateY(0px) translateX(0px);
          }
          33% {
            transform: translateY(-10px) translateX(5px);
          }
          66% {
            transform: translateY(5px) translateX(-5px);
          }
        }

        @keyframes twinkle {
          0%,
          100% {
            opacity: 0.3;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.5);
          }
        }

        @keyframes glowPulse {
          0%,
          100% {
            text-shadow: 0 0 5px rgba(233, 69, 96, 0.5),
              0 0 10px rgba(233, 69, 96, 0.3);
          }
          50% {
            text-shadow: 0 0 10px rgba(233, 69, 96, 0.8),
              0 0 20px rgba(233, 69, 96, 0.5), 0 0 30px rgba(233, 69, 96, 0.3);
          }
        }

        @keyframes textShimmer {
          0%,
          100% {
            background-position: -200% center;
          }
          50% {
            background-position: 200% center;
          }
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes cardFloat {
          0%,
          100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        @keyframes statsReveal {
          from {
            opacity: 0;
            transform: translateY(20px) scale(0.9);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @keyframes iconPulse {
          0%,
          100% {
            color: #e94560;
            transform: scale(1);
          }
          50% {
            color: #ff6b8a;
            transform: scale(1.1);
          }
        }
      `}</style>
    </section>
  );
}
