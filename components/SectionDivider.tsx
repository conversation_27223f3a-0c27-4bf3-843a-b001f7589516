"use client";

export default function SectionDivider() {
  return (
    <>
      {/* SVG Visual Blending Element for Smooth Transition */}
      <div className="absolute inset-0">
        <svg
          className="w-full h-full shadow-2xl"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
        >
          {/* Visual Blending Element for Smooth Transition */}
          <g className="transition-blend-element">
            {/* Main blending wave */}
            <path
              d="M0,85 Q25,80 50,85 T100,85 L100,100 L0,100 Z"
              fill="url(#blendGradient)"
              opacity="0.8"
              className="animate-wave-flow"
            />

            {/* Secondary blending layer */}
            <path
              d="M0,90 Q30,85 60,90 T100,90 L100,100 L0,100 Z"
              fill="url(#blendGradient2)"
              opacity="0.6"
              className="animate-wave-flow-delayed"
            />

            {/* Particle flow effect */}
            {Array.from({ length: 15 }, (_, i) => {
              const x = (i * 7) % 100;
              const y = 88 + Math.sin(i * 0.5) * 2;

              return (
                <circle
                  key={`blend-particle-${i}`}
                  cx={x}
                  cy={y}
                  r="0.08"
                  fill="#E94560"
                  opacity="0.7"
                  className="animate-particle-flow"
                  style={{ animationDelay: `${i * 0.3}s` }}
                />
              );
            })}
          </g>

          <defs>
            {/* Blending gradients for smooth transition */}
            <linearGradient
              id="blendGradient"
              x1="0%"
              y1="0%"
              x2="0%"
              y2="100%"
            >
              <stop
                offset="0%"
                stopColor="hsl(var(--space-dark))"
                stopOpacity="0.9"
              >
                <animate
                  attributeName="stop-opacity"
                  values="0.9;0.7;0.9"
                  dur="6s"
                  repeatCount="indefinite"
                />
              </stop>
              <stop
                offset="30%"
                stopColor="hsl(var(--space-primary))"
                stopOpacity="0.8"
              >
                <animate
                  attributeName="stop-opacity"
                  values="0.8;0.6;0.8"
                  dur="6s"
                  repeatCount="indefinite"
                />
              </stop>
              <stop
                offset="70%"
                stopColor="hsl(var(--space-secondary))"
                stopOpacity="0.6"
              >
                <animate
                  attributeName="stop-opacity"
                  values="0.6;0.4;0.6"
                  dur="6s"
                  repeatCount="indefinite"
                />
              </stop>
              <stop offset="100%" stopColor="transparent" stopOpacity="0" />
            </linearGradient>

            <linearGradient
              id="blendGradient2"
              x1="0%"
              y1="0%"
              x2="0%"
              y2="100%"
            >
              <stop offset="0%" stopColor="#E94560" stopOpacity="0.3">
                <animate
                  attributeName="stop-opacity"
                  values="0.3;0.1;0.3"
                  dur="8s"
                  repeatCount="indefinite"
                />
              </stop>
              <stop offset="50%" stopColor="#A855F7" stopOpacity="0.2">
                <animate
                  attributeName="stop-opacity"
                  values="0.2;0.4;0.2"
                  dur="8s"
                  repeatCount="indefinite"
                />
              </stop>
              <stop offset="100%" stopColor="transparent" stopOpacity="0" />
            </linearGradient>
          </defs>
        </svg>
      </div>

      {/* Additional CSS Blending Layer */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[hsl(var(--space-dark))] via-[hsl(var(--space-primary))]/80 to-transparent mix-blend-multiply opacity-90 pointer-events-none"></div>

      {/* Morphing transition overlay */}
      <div className="absolute bottom-0 left-0 right-0 h-24 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-t from-[hsl(var(--space-dark))] to-transparent opacity-70"></div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-r from-primary/10 via-[hsl(var(--space-accent))]/10 to-primary/10 animate-gradient-flow"></div>
      </div>

      {/* Custom CSS for divider animations */}
      <style jsx>{`
        .animate-wave-flow {
          animation: waveFlow 8s ease-in-out infinite;
        }

        .animate-wave-flow-delayed {
          animation: waveFlow 10s ease-in-out infinite;
          animation-delay: 2s;
        }

        .animate-particle-flow {
          animation: particleFlow 6s ease-in-out infinite;
        }

        .animate-gradient-flow {
          animation: gradientFlow 12s linear infinite;
        }

        @keyframes waveFlow {
          0%,
          100% {
            transform: translateY(0) scaleY(1);
            opacity: 0.8;
          }
          50% {
            transform: translateY(-2px) scaleY(1.05);
            opacity: 0.6;
          }
        }

        @keyframes particleFlow {
          0%,
          100% {
            transform: translateX(0) translateY(0) scale(1);
            opacity: 0.7;
          }
          25% {
            transform: translateX(2px) translateY(-1px) scale(1.1);
            opacity: 0.9;
          }
          50% {
            transform: translateX(-1px) translateY(1px) scale(0.9);
            opacity: 0.5;
          }
          75% {
            transform: translateX(-2px) translateY(-0.5px) scale(1.05);
            opacity: 0.8;
          }
        }

        @keyframes gradientFlow {
          0% {
            background-position: -200% center;
            opacity: 0.3;
          }
          50% {
            background-position: 200% center;
            opacity: 0.6;
          }
          100% {
            background-position: -200% center;
            opacity: 0.3;
          }
        }
      `}</style>
    </>
  );
}
