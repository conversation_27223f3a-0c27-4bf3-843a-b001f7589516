"use client";

import { ReactNode, useEffect, useState, useCallback } from "react";
import { cn } from "@/lib/utils";

interface SliderItem {
  id: string;
  content: ReactNode;
  height?: number;
}

interface InfiniteVerticalSliderProps {
  items: SliderItem[];
  speed?: number;
  columns?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: number;
  className?: string;
  itemClassName?: string;
  pauseOnHover?: boolean;
  blendHeight?: number;
  blendColors?: {
    top?: string;
    bottom?: string;
  };
  portalEffect?: boolean;
  vignetteIntensity?: number;
  sophisticatedShadowEffect?: boolean;
}

const InfiniteVerticalSlider = ({
  items,
  speed = 30,
  columns = { sm: 1, md: 2, lg: 3, xl: 4 },
  gap = 16,
  className,
  itemClassName,
  pauseOnHover = true,
  blendHeight = 100,
  blendColors = {
    top: "linear-gradient(to bottom, hsl(var(--space-dark)) 0%, hsl(var(--space-primary)) 20%, rgba(26, 26, 46, 0.8) 60%, transparent 100%)",
    bottom:
      "linear-gradient(to top, hsl(var(--space-primary)) 0%, hsl(var(--space-dark)) 20%, rgba(26, 26, 46, 0.8) 60%, transparent 100%)",
  },
  portalEffect = false,
  vignetteIntensity = 0.7,
  sophisticatedShadowEffect = false,
}: InfiniteVerticalSliderProps) => {
  const [isPaused, setIsPaused] = useState(false);
  const [currentColumns, setCurrentColumns] = useState(columns.md || 2);
  const [isMounted, setIsMounted] = useState(false);

  // Ensure component is mounted on client before using window
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Generate portal effect styles using colors from the provided screenshot
  const getPortalBlendStyles = useCallback(() => {
    // Colors extracted from the screenshot with main color #11132C
    const themeColor = "#11132C"; // Main dark navy color
    const subtleBlue = "#3B82F6";
    const darkPurple = "#6366F1";

    if (!portalEffect) {
      return {
        top:
          blendColors.top ||
          `
          radial-gradient(ellipse 150% 100% at center top,
            ${themeColor}60 0%,
            ${subtleBlue}30 40%,
            ${darkPurple}20 70%,
            transparent 100%
          ),
          linear-gradient(to bottom,
            ${themeColor} 0%,
            ${subtleBlue}15 50%,
            transparent 100%
          )
        `,
        bottom:
          blendColors.bottom ||
          `
          radial-gradient(ellipse 150% 100% at center bottom,
            ${themeColor}60 0%,
            ${subtleBlue}30 40%,
            ${darkPurple}20 70%,
            transparent 100%
          ),
          linear-gradient(to top,
            ${themeColor} 0%,
            ${subtleBlue}15 50%,
            transparent 100%
          )
        `,
      };
    }

    const vignetteOpacity = vignetteIntensity;

    return {
      top: `
        radial-gradient(ellipse 200% 120% at center top,
          ${themeColor}${Math.round(vignetteOpacity * 0.8 * 255)
        .toString(16)
        .padStart(2, "0")} 0%,
          ${subtleBlue}${Math.round(vignetteOpacity * 0.4 * 255)
        .toString(16)
        .padStart(2, "0")} 40%,
          ${darkPurple}${Math.round(vignetteOpacity * 0.3 * 255)
        .toString(16)
        .padStart(2, "0")} 70%,
          transparent 100%
        ),
        linear-gradient(to bottom,
          ${themeColor} 0%,
          ${subtleBlue}20 40%,
          transparent 100%
        )
      `,
      bottom: `
        radial-gradient(ellipse 200% 120% at center bottom,
          ${themeColor}${Math.round(vignetteOpacity * 0.8 * 255)
        .toString(16)
        .padStart(2, "0")} 0%,
          ${subtleBlue}${Math.round(vignetteOpacity * 0.4 * 255)
        .toString(16)
        .padStart(2, "0")} 40%,
          ${darkPurple}${Math.round(vignetteOpacity * 0.3 * 255)
        .toString(16)
        .padStart(2, "0")} 70%,
          transparent 100%
        ),
        linear-gradient(to top,
          ${themeColor} 0%,
          ${subtleBlue}20 40%,
          transparent 100%
        )
      `,
    };
  }, [portalEffect, blendColors, vignetteIntensity]);

  const calculateMasonryLayout = (items: SliderItem[], columnCount: number) => {
    const columns: SliderItem[][] = Array.from(
      { length: columnCount },
      () => []
    );
    items.forEach((item, index) => {
      const columnIndex = index % columnCount;
      columns[columnIndex].push(item);
    });
    return columns;
  };

  const getCurrentColumnCount = useCallback(() => {
    // Return default value during SSR
    if (!isMounted || typeof window === "undefined") return columns.md || 2;
    
    const width = window.innerWidth;
    if (width >= 1280) return columns.xl || 4;
    if (width >= 1024) return columns.lg || 3;
    if (width >= 768) return columns.md || 2;
    return columns.sm || 1;
  }, [columns, isMounted]);

  useEffect(() => {
    // Only run on client side after mounting
    if (!isMounted || typeof window === "undefined") {
      return;
    }

    const handleResize = () => {
      setCurrentColumns(getCurrentColumnCount());
    };
    
    setCurrentColumns(getCurrentColumnCount());
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [columns, getCurrentColumnCount, isMounted]);

  const duplicatedItems = [...items, ...items];
  const masonryColumns = calculateMasonryLayout(
    duplicatedItems,
    currentColumns
  );
  const currentBlendStyles = getPortalBlendStyles();

  const handleMouseEnter = () => {
    if (pauseOnHover) setIsPaused(true);
  };

  const handleMouseLeave = () => {
    if (pauseOnHover) setIsPaused(false);
  };

  return (
    <div
      className={cn(
        "relative overflow-hidden",
        portalEffect && "portal-window",
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={
        portalEffect
          ? {
              borderRadius: "24px",
              boxShadow: `
          inset 0 0 60px rgba(26, 26, 46, ${vignetteIntensity * 0.6}),
          inset 0 0 120px rgba(26, 26, 46, ${vignetteIntensity * 0.3}),
          0 0 40px rgba(26, 26, 46, 0.4)
        `,
              transform: "perspective(1000px) rotateX(1deg)",
              transformStyle: "preserve-3d",
            }
          : {}
      }
    >
      {/* Enhanced Top blend effect with SVG animations */}
      <div
        className="absolute top-0 left-0 right-0 z-10 pointer-events-none"
        style={{
          height: `${blendHeight}px`,
          background: sophisticatedShadowEffect
            ? "transparent"
            : currentBlendStyles.top,
        }}
      >
        {sophisticatedShadowEffect ? (
          <svg
            className="absolute inset-0 w-full h-full"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <defs>
              {/* Sophisticated Shadow Gradients */}
              <radialGradient id="insetShadowTop" cx="50%" cy="0%" r="100%">
                <stop
                  offset="0%"
                  stopColor="hsl(var(--space-dark))"
                  stopOpacity="1"
                >
                  <animate
                    attributeName="stop-opacity"
                    values="1;0.8;1"
                    dur="4s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop
                  offset="30%"
                  stopColor="hsl(var(--space-primary))"
                  stopOpacity="0.8"
                >
                  <animate
                    attributeName="stop-opacity"
                    values="0.8;0.6;0.8"
                    dur="5s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop
                  offset="70%"
                  stopColor="hsl(var(--space-primary))"
                  stopOpacity="0.4"
                >
                  <animate
                    attributeName="stop-opacity"
                    values="0.4;0.2;0.4"
                    dur="6s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="100%" stopColor="transparent" stopOpacity="0" />
              </radialGradient>

              <linearGradient
                id="outsetShadowTop"
                x1="0%"
                y1="0%"
                x2="0%"
                y2="100%"
              >
                <stop
                  offset="0%"
                  stopColor="hsl(var(--space-dark))"
                  stopOpacity="0.9"
                >
                  <animate
                    attributeName="stop-opacity"
                    values="0.9;0.7;0.9"
                    dur="3.5s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop
                  offset="50%"
                  stopColor="hsl(var(--space-primary))"
                  stopOpacity="0.5"
                >
                  <animate
                    attributeName="stop-opacity"
                    values="0.5;0.3;0.5"
                    dur="4.5s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="100%" stopColor="transparent" stopOpacity="0" />
              </linearGradient>

              {/* Advanced Shadow Filters */}
              <filter
                id="insetShadowFilter"
                x="-50%"
                y="-50%"
                width="200%"
                height="200%"
              >
                <feGaussianBlur
                  in="SourceGraphic"
                  stdDeviation="1.5"
                  result="blur"
                />
                <feOffset in="blur" dx="0" dy="2" result="offsetBlur" />
                <feFlood
                  floodColor="hsl(var(--space-dark))"
                  floodOpacity="0.6"
                  result="floodColor"
                />
                <feComposite
                  in="floodColor"
                  in2="offsetBlur"
                  operator="in"
                  result="shadow"
                />
                <feMerge>
                  <feMergeNode in="shadow" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
                <animate
                  attributeName="stdDeviation"
                  values="1.5;2.5;1.5"
                  dur="4s"
                  repeatCount="indefinite"
                />
              </filter>

              <filter
                id="outsetShadowFilter"
                x="-50%"
                y="-50%"
                width="200%"
                height="200%"
              >
                <feGaussianBlur
                  in="SourceGraphic"
                  stdDeviation="2"
                  result="blur"
                />
                <feOffset in="blur" dx="0" dy="-1" result="offsetBlur" />
                <feFlood
                  floodColor="hsl(var(--space-primary))"
                  floodOpacity="0.4"
                  result="floodColor"
                />
                <feComposite
                  in="floodColor"
                  in2="offsetBlur"
                  operator="in"
                  result="shadow"
                />
                <feMerge>
                  <feMergeNode in="SourceGraphic" />
                  <feMergeNode in="shadow" />
                </feMerge>
                <animate
                  attributeName="stdDeviation"
                  values="2;3;2"
                  dur="5s"
                  repeatCount="indefinite"
                />
              </filter>

              {/* Breathing Depth Effect */}
              <filter
                id="breathingDepth"
                x="-50%"
                y="-50%"
                width="200%"
                height="200%"
              >
                <feGaussianBlur
                  in="SourceGraphic"
                  stdDeviation="1"
                  result="blur"
                />
                <feColorMatrix
                  in="blur"
                  type="matrix"
                  values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 0.8 0"
                  result="coloredBlur"
                />
                <feMorphology
                  in="coloredBlur"
                  operator="dilate"
                  radius="0.5"
                  result="dilated"
                />
                <feMorphology
                  in="coloredBlur"
                  operator="erode"
                  radius="0.5"
                  result="eroded"
                />
                <feComposite
                  in="dilated"
                  in2="eroded"
                  operator="over"
                  result="combined"
                />
                <feMerge>
                  <feMergeNode in="combined" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
                <animate
                  attributeName="radius"
                  values="0.5;1.2;0.5"
                  dur="6s"
                  repeatCount="indefinite"
                />
              </filter>
            </defs>

            {/* Layered Shadow Effects */}
            <g className="sophisticated-shadow-system">
              {/* Inset Shadow Layer */}
              <rect
                x="0"
                y="0"
                width="100"
                height="100"
                fill="url(#insetShadowTop)"
                filter="url(#insetShadowFilter)"
                opacity="0.8"
              >
                <animate
                  attributeName="opacity"
                  values="0.8;0.6;0.8"
                  dur="4s"
                  repeatCount="indefinite"
                />
              </rect>

              {/* Outset Shadow Layer */}
              <rect
                x="0"
                y="0"
                width="100"
                height="100"
                fill="url(#outsetShadowTop)"
                filter="url(#outsetShadowFilter)"
                opacity="0.6"
              >
                <animate
                  attributeName="opacity"
                  values="0.6;0.4;0.6"
                  dur="5s"
                  repeatCount="indefinite"
                />
              </rect>

              {/* Breathing Depth Layer */}
              <rect
                x="0"
                y="0"
                width="100"
                height="100"
                fill="none"
                stroke="hsl(var(--space-primary))"
                strokeWidth="0.1"
                filter="url(#breathingDepth)"
                opacity="0.3"
              >
                <animate
                  attributeName="stroke-width"
                  values="0.1;0.3;0.1"
                  dur="6s"
                  repeatCount="indefinite"
                />
                <animate
                  attributeName="opacity"
                  values="0.3;0.1;0.3"
                  dur="4s"
                  repeatCount="indefinite"
                />
              </rect>
            </g>
          </svg>
        ) : (
          <svg
            className="absolute inset-0 w-full h-full"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <defs>
              <linearGradient
                id="topBlendGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.4">
                  <animate
                    attributeName="stop-opacity"
                    values="0.4;0.2;0.4"
                    dur="4s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="50%" stopColor="#6366F1" stopOpacity="0.3">
                  <animate
                    attributeName="stop-opacity"
                    values="0.3;0.1;0.3"
                    dur="5s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="100%" stopColor="transparent" stopOpacity="0" />
              </linearGradient>
              <radialGradient id="topBlendGradient2" cx="50%" cy="0%" r="100%">
                <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3">
                  <animate
                    attributeName="stop-opacity"
                    values="0.3;0.1;0.3"
                    dur="6s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="70%" stopColor="#6366F1" stopOpacity="0.2">
                  <animate
                    attributeName="stop-opacity"
                    values="0.2;0.05;0.2"
                    dur="7s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="100%" stopColor="transparent" stopOpacity="0" />
              </radialGradient>
              <filter id="glow">
                <feGaussianBlur stdDeviation="2" result="coloredBlur" />
                <feMerge>
                  <feMergeNode in="coloredBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>
            </defs>

            {/* Top wave animations with screenshot colors */}
            <g className="transition-blend-element">
              <path
                d="M0,0 L100,0 L100,20 Q75,25 50,20 Q25,15 0,20 Z"
                fill="url(#topBlendGradient)"
                opacity="0.6"
                className="animate-wave-flow"
                filter="url(#glow)"
              />
              <path
                d="M0,0 L100,0 L100,15 Q70,20 40,15 T0,15 Z"
                fill="url(#topBlendGradient2)"
                opacity="0.5"
                className="animate-wave-flow-delayed"
                filter="url(#glow)"
              />

              {/* Subtle particle effects */}
              {Array.from({ length: 12 }, (_, i) => {
                const x = (i * 8.5) % 100;
                const y = 8 + Math.sin(i * 0.6) * 4;
                const colors = ["#3B82F6", "#6366F1", "#8B5CF6"];
                const color = colors[i % colors.length];

                return (
                  <circle
                    key={`top-particle-${i}`}
                    cx={x}
                    cy={y}
                    r="0.2"
                    fill={color}
                    opacity="0.6"
                    className="animate-particle-flow"
                    filter="url(#glow)"
                    style={{ animationDelay: `${i * 0.3}s` }}
                  >
                    <animate
                      attributeName="r"
                      values="0.2;0.4;0.2"
                      dur="3s"
                      repeatCount="indefinite"
                    />
                    <animate
                      attributeName="opacity"
                      values="0.6;0.2;0.6"
                      dur="4s"
                      repeatCount="indefinite"
                    />
                  </circle>
                );
              })}

              {/* Energy orbs */}
              {Array.from({ length: 6 }, (_, i) => {
                const x = 16.67 + i * 16.67;
                const y = 12;

                return (
                  <circle
                    key={`top-orb-${i}`}
                    cx={x}
                    cy={y}
                    r="1"
                    fill="none"
                    stroke="#3B82F6"
                    strokeWidth="0.1"
                    opacity="0.4"
                    filter="url(#glow)"
                  >
                    <animate
                      attributeName="r"
                      values="1;2.5;1"
                      dur="4s"
                      repeatCount="indefinite"
                      begin={`${i * 0.7}s`}
                    />
                    <animate
                      attributeName="opacity"
                      values="0.4;0.1;0.4"
                      dur="4s"
                      repeatCount="indefinite"
                      begin={`${i * 0.7}s`}
                    />
                  </circle>
                );
              })}
            </g>
          </svg>
        )}
      </div>

      {/* Slider container */}
      <div
        className="flex"
        style={
          portalEffect
            ? {
                gap: `${gap}px`,
                transform: "translateZ(-20px) scale(1.02)",
                transformStyle: "preserve-3d",
                filter: `brightness(0.95) contrast(1.05)`,
              }
            : { gap: `${gap}px` }
        }
      >
        {masonryColumns.map((column, columnIndex) => (
          <div
            key={columnIndex}
            className={cn(
              "flex-1 flex flex-col animate-infinite-scroll",
              isPaused && "animate-paused"
            )}
            style={{
              gap: `${gap}px`,
              animationDuration: `${speed}s`,
              animationDelay: `${columnIndex * -1}s`,
            }}
          >
            {column.map((item, itemIndex) => (
              <div
                key={`${item.id}-${itemIndex}`}
                className={cn(
                  "glassmorphism rounded-2xl p-4 hover-glow transition-all duration-300 flex-shrink-0",
                  itemClassName
                )}
              >
                {item.content}
              </div>
            ))}
          </div>
        ))}
      </div>

      {/* Enhanced Bottom blend effect with SVG animations */}
      <div
        className="absolute bottom-0 left-0 right-0 z-10 pointer-events-none"
        style={{
          height: `${blendHeight}px`,
          background: sophisticatedShadowEffect
            ? "transparent"
            : currentBlendStyles.bottom,
        }}
      >
        {sophisticatedShadowEffect ? (
          <svg
            className="absolute inset-0 w-full h-full"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <defs>
              {/* Sophisticated Bottom Shadow Gradients */}
              <radialGradient
                id="insetShadowBottom"
                cx="50%"
                cy="100%"
                r="100%"
              >
                <stop
                  offset="0%"
                  stopColor="hsl(var(--space-dark))"
                  stopOpacity="1"
                >
                  <animate
                    attributeName="stop-opacity"
                    values="1;0.8;1"
                    dur="4.5s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop
                  offset="30%"
                  stopColor="hsl(var(--space-primary))"
                  stopOpacity="0.8"
                >
                  <animate
                    attributeName="stop-opacity"
                    values="0.8;0.6;0.8"
                    dur="5.5s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop
                  offset="70%"
                  stopColor="hsl(var(--space-primary))"
                  stopOpacity="0.4"
                >
                  <animate
                    attributeName="stop-opacity"
                    values="0.4;0.2;0.4"
                    dur="6.5s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="100%" stopColor="transparent" stopOpacity="0" />
              </radialGradient>

              <linearGradient
                id="outsetShadowBottom"
                x1="0%"
                y1="100%"
                x2="0%"
                y2="0%"
              >
                <stop
                  offset="0%"
                  stopColor="hsl(var(--space-dark))"
                  stopOpacity="0.9"
                >
                  <animate
                    attributeName="stop-opacity"
                    values="0.9;0.7;0.9"
                    dur="4s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop
                  offset="50%"
                  stopColor="hsl(var(--space-primary))"
                  stopOpacity="0.5"
                >
                  <animate
                    attributeName="stop-opacity"
                    values="0.5;0.3;0.5"
                    dur="5s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="100%" stopColor="transparent" stopOpacity="0" />
              </linearGradient>

              {/* Advanced Bottom Shadow Filters */}
              <filter
                id="insetShadowFilterBottom"
                x="-50%"
                y="-50%"
                width="200%"
                height="200%"
              >
                <feGaussianBlur
                  in="SourceGraphic"
                  stdDeviation="1.8"
                  result="blur"
                />
                <feOffset in="blur" dx="0" dy="-2" result="offsetBlur" />
                <feFlood
                  floodColor="hsl(var(--space-dark))"
                  floodOpacity="0.7"
                  result="floodColor"
                />
                <feComposite
                  in="floodColor"
                  in2="offsetBlur"
                  operator="in"
                  result="shadow"
                />
                <feMerge>
                  <feMergeNode in="shadow" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
                <animate
                  attributeName="stdDeviation"
                  values="1.8;2.8;1.8"
                  dur="4.5s"
                  repeatCount="indefinite"
                />
              </filter>

              <filter
                id="outsetShadowFilterBottom"
                x="-50%"
                y="-50%"
                width="200%"
                height="200%"
              >
                <feGaussianBlur
                  in="SourceGraphic"
                  stdDeviation="2.2"
                  result="blur"
                />
                <feOffset in="blur" dx="0" dy="1" result="offsetBlur" />
                <feFlood
                  floodColor="hsl(var(--space-primary))"
                  floodOpacity="0.5"
                  result="floodColor"
                />
                <feComposite
                  in="floodColor"
                  in2="offsetBlur"
                  operator="in"
                  result="shadow"
                />
                <feMerge>
                  <feMergeNode in="SourceGraphic" />
                  <feMergeNode in="shadow" />
                </feMerge>
                <animate
                  attributeName="stdDeviation"
                  values="2.2;3.2;2.2"
                  dur="5.5s"
                  repeatCount="indefinite"
                />
              </filter>

              {/* Breathing Depth Effect Bottom */}
              <filter
                id="breathingDepthBottom"
                x="-50%"
                y="-50%"
                width="200%"
                height="200%"
              >
                <feGaussianBlur
                  in="SourceGraphic"
                  stdDeviation="1.2"
                  result="blur"
                />
                <feColorMatrix
                  in="blur"
                  type="matrix"
                  values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 0.9 0"
                  result="coloredBlur"
                />
                <feMorphology
                  in="coloredBlur"
                  operator="dilate"
                  radius="0.6"
                  result="dilated"
                />
                <feMorphology
                  in="coloredBlur"
                  operator="erode"
                  radius="0.6"
                  result="eroded"
                />
                <feComposite
                  in="dilated"
                  in2="eroded"
                  operator="over"
                  result="combined"
                />
                <feMerge>
                  <feMergeNode in="combined" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
                <animate
                  attributeName="radius"
                  values="0.6;1.4;0.6"
                  dur="6.5s"
                  repeatCount="indefinite"
                />
              </filter>
            </defs>

            {/* Layered Bottom Shadow Effects */}
            <g className="sophisticated-shadow-system-bottom">
              {/* Inset Shadow Layer */}
              <rect
                x="0"
                y="0"
                width="100"
                height="100"
                fill="url(#insetShadowBottom)"
                filter="url(#insetShadowFilterBottom)"
                opacity="0.8"
              >
                <animate
                  attributeName="opacity"
                  values="0.8;0.6;0.8"
                  dur="4.5s"
                  repeatCount="indefinite"
                />
              </rect>

              {/* Outset Shadow Layer */}
              <rect
                x="0"
                y="0"
                width="100"
                height="100"
                fill="url(#outsetShadowBottom)"
                filter="url(#outsetShadowFilterBottom)"
                opacity="0.6"
              >
                <animate
                  attributeName="opacity"
                  values="0.6;0.4;0.6"
                  dur="5.5s"
                  repeatCount="indefinite"
                />
              </rect>

              {/* Breathing Depth Layer */}
              <rect
                x="0"
                y="0"
                width="100"
                height="100"
                fill="none"
                stroke="hsl(var(--space-primary))"
                strokeWidth="0.1"
                filter="url(#breathingDepthBottom)"
                opacity="0.3"
              >
                <animate
                  attributeName="stroke-width"
                  values="0.1;0.3;0.1"
                  dur="6.5s"
                  repeatCount="indefinite"
                />
                <animate
                  attributeName="opacity"
                  values="0.3;0.1;0.3"
                  dur="4.5s"
                  repeatCount="indefinite"
                />
              </rect>
            </g>
          </svg>
        ) : (
          <svg
            className="absolute inset-0 w-full h-full"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <defs>
              <linearGradient
                id="bottomBlendGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="transparent" stopOpacity="0" />
                <stop offset="30%" stopColor="#3B82F6" stopOpacity="0.4">
                  <animate
                    attributeName="stop-opacity"
                    values="0.4;0.2;0.4"
                    dur="5s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="100%" stopColor="#6366F1" stopOpacity="0.5">
                  <animate
                    attributeName="stop-opacity"
                    values="0.5;0.3;0.5"
                    dur="6s"
                    repeatCount="indefinite"
                  />
                </stop>
              </linearGradient>
              <radialGradient
                id="bottomBlendGradient2"
                cx="50%"
                cy="100%"
                r="120%"
              >
                <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3">
                  <animate
                    attributeName="stop-opacity"
                    values="0.3;0.1;0.3"
                    dur="6s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="60%" stopColor="#6366F1" stopOpacity="0.2">
                  <animate
                    attributeName="stop-opacity"
                    values="0.2;0.05;0.2"
                    dur="5s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="100%" stopColor="transparent" stopOpacity="0" />
              </radialGradient>
              <linearGradient
                id="galaxyGradient1"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.4">
                  <animate
                    attributeName="stop-color"
                    values="#3B82F6;#6366F1;#8B5CF6;#3B82F6"
                    dur="8s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="50%" stopColor="#6366F1" stopOpacity="0.3">
                  <animate
                    attributeName="stop-color"
                    values="#6366F1;#8B5CF6;#3B82F6;#6366F1"
                    dur="10s"
                    repeatCount="indefinite"
                  />
                </stop>
                <stop offset="100%" stopColor="#8B5CF6" stopOpacity="0.2">
                  <animate
                    attributeName="stop-color"
                    values="#8B5CF6;#3B82F6;#6366F1;#8B5CF6"
                    dur="12s"
                    repeatCount="indefinite"
                  />
                </stop>
              </linearGradient>
            </defs>

            {/* Bottom wave animations */}
            <g className="transition-blend-element">
              <path
                d="M0,80 Q25,75 50,80 Q75,85 100,80 L100,100 L0,100 Z"
                fill="url(#bottomBlendGradient)"
                opacity="0.6"
                className="animate-wave-flow"
                filter="url(#glow)"
              />
              <path
                d="M0,85 Q30,80 60,85 T100,85 L100,100 L0,100 Z"
                fill="url(#bottomBlendGradient2)"
                opacity="0.5"
                className="animate-wave-flow-delayed"
                filter="url(#glow)"
              />

              {/* Flowing energy stream */}
              <path
                d="M0,82 Q25,77 50,82 Q75,87 100,82"
                stroke="#6366F1"
                strokeWidth="0.3"
                fill="none"
                opacity="0.4"
                filter="url(#glow)"
              >
                <animate
                  attributeName="stroke"
                  values="#6366F1;#3B82F6;#8B5CF6;#6366F1"
                  dur="6s"
                  repeatCount="indefinite"
                />
                <animate
                  attributeName="opacity"
                  values="0.4;0.2;0.4"
                  dur="5s"
                  repeatCount="indefinite"
                />
              </path>

              {/* Bottom particle effects */}
              {Array.from({ length: 15 }, (_, i) => {
                const x = (i * 6.67) % 100;
                const y = 78 + Math.sin(i * 1.2) * 8;
                const colors = ["#3B82F6", "#6366F1", "#8B5CF6"];
                const color = colors[i % colors.length];

                return (
                  <circle
                    key={`bottom-particle-${i}`}
                    cx={x}
                    cy={y}
                    r="0.25"
                    fill={color}
                    opacity="0.6"
                    className="animate-particle-flow"
                    filter="url(#glow)"
                    style={{ animationDelay: `${i * 0.2}s` }}
                  >
                    <animate
                      attributeName="r"
                      values="0.25;0.5;0.25"
                      dur="4s"
                      repeatCount="indefinite"
                    />
                    <animate
                      attributeName="opacity"
                      values="0.6;0.2;0.6"
                      dur="3s"
                      repeatCount="indefinite"
                    />
                  </circle>
                );
              })}

              {/* Galaxy spiral connections */}
              <g
                className="animate-spin-slow origin-center"
                style={{ transformOrigin: "50% 85%" }}
              >
                <path
                  d="M20,82 Q35,77 50,82 Q65,87 80,82"
                  stroke="url(#galaxyGradient1)"
                  strokeWidth="0.2"
                  fill="none"
                  opacity="0.3"
                  filter="url(#glow)"
                  className="animate-pulse-slow"
                />
              </g>

              {/* Energy orbs */}
              {Array.from({ length: 5 }, (_, i) => {
                const x = 20 + i * 15;
                const y = 85;

                return (
                  <circle
                    key={`bottom-orb-${i}`}
                    cx={x}
                    cy={y}
                    r="1.2"
                    fill="none"
                    stroke="#3B82F6"
                    strokeWidth="0.15"
                    opacity="0.4"
                    filter="url(#glow)"
                  >
                    <animate
                      attributeName="r"
                      values="1.2;3;1.2"
                      dur="5s"
                      repeatCount="indefinite"
                      begin={`${i * 1}s`}
                    />
                    <animate
                      attributeName="opacity"
                      values="0.4;0.1;0.4"
                      dur="5s"
                      repeatCount="indefinite"
                      begin={`${i * 1}s`}
                    />
                    <animate
                      attributeName="stroke"
                      values="#3B82F6;#6366F1;#8B5CF6;#3B82F6"
                      dur="8s"
                      repeatCount="indefinite"
                    />
                  </circle>
                );
              })}
            </g>
          </svg>
        )}
      </div>
    </div>
  );
};

export default InfiniteVerticalSlider;
