import * as React from "react"
import { cn } from "@/lib/utils"
import { badgeVariants } from "@/components/ui/type"

/**
 * Badge component props interface
 */
interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  variant?: "default" | "secondary" | "destructive" | "outline";
}

/**
 * Badge component to display a badge with a variant and custom className.
 * 
 * @param props - The component props
 * @returns The Badge component
 */
function Badge({
  className,
  variant,
  ...props
}: BadgeProps) {
  return (<div className={cn(badgeVariants({ variant }), className)} {...props} />);
}

export { Badge, badgeVariants }
