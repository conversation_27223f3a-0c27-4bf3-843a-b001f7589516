"use client";

import { useScrollAnimation } from "@/hooks/useScrollAnimation";
import { ReactElement } from "react";
import InfiniteVerticalSlider from "@/components/ui/InfiniteVerticalSlider";

interface Review {
  name: string;
  company: string;
  rating: number;
  text: string;
  project: string;
}

const reviews: Review[] = [
  {
    name: "<PERSON>",
    company: "TechStart Inc.",
    rating: 5,
    text: "Outstanding work! <PERSON><PERSON><PERSON><PERSON> delivered a complex ClickFunnels integration that exceeded our expectations. His attention to detail and communication throughout the project was exceptional.",
    project: "ClickFunnels Custom Integration",
  },
  {
    name: "<PERSON>",
    company: "Digital Marketing Pro",
    rating: 5,
    text: "Fantastic developer! Built our entire GoHighLevel automation system flawlessly. Fast delivery, clean code, and excellent support. Highly recommend!",
    project: "GoHighLevel Automation",
  },
  {
    name: "<PERSON>",
    company: "E-commerce Solutions",
    rating: 5,
    text: "Al-<PERSON><PERSON> transformed our Shopify store with custom features and integrations. The performance improvements were incredible. Professional and reliable!",
    project: "Shopify Custom Development",
  },
  {
    name: "<PERSON>",
    company: "Marketing Agency Plus",
    rating: 5,
    text: "Excellent React developer! Created a beautiful, responsive web application that perfectly matched our design requirements. Great communication and timely delivery.",
    project: "React Web Application",
  },
  {
    name: "<PERSON> <PERSON>",
    company: "SaaS Startup",
    rating: 5,
    text: "Incredible work on our Laravel backend API. Al-Amin's expertise in both frontend and backend development made our project seamless. Will definitely work with him again!",
    project: "Laravel API Development",
  },
  {
    name: "James Miller",
    company: "Digital Solutions Co.",
    rating: 5,
    text: "Top-notch Vue.js development! Al-Amin built our dashboard application with clean, maintainable code. His understanding of modern web technologies is impressive.",
    project: "Vue.js Dashboard",
  },
  {
    name: "Rachel Kim",
    company: "FinTech Solutions",
    rating: 5,
    text: "Al-Amin's expertise in blockchain development helped us launch our DeFi platform successfully. His deep understanding of smart contracts and security best practices was invaluable.",
    project: "DeFi Platform Development",
  },
  {
    name: "Marcus Brown",
    company: "Healthcare Tech",
    rating: 5,
    text: "Delivered a robust telemedicine platform with real-time video integration. The solution exceeded our expectations and was delivered ahead of schedule.",
    project: "Telemedicine Platform",
  },
  {
    name: "Anna Petrov",
    company: "EdTech Innovations",
    rating: 5,
    text: "Created an amazing learning management system with interactive features. Students love the user experience and teachers find it incredibly intuitive.",
    project: "LMS Development",
  },
  {
    name: "Carlos Martinez",
    company: "Real Estate Pro",
    rating: 5,
    text: "Built a comprehensive property management system with advanced search and analytics. The automation features have saved us countless hours.",
    project: "Property Management System",
  },
  {
    name: "Sophie Turner",
    company: "Fashion Forward",
    rating: 5,
    text: "Developed a stunning e-commerce platform with AR try-on features. Our sales increased by 40% after the launch. Absolutely phenomenal work!",
    project: "AR E-commerce Platform",
  },
  {
    name: "Ahmed Hassan",
    company: "Logistics Hub",
    rating: 5,
    text: "Created a real-time tracking system that revolutionized our delivery operations. The GPS integration and route optimization are game-changers.",
    project: "Logistics Tracking System",
  },
];

export default function ReviewsSection() {
  const { ref, isVisible } = useScrollAnimation();

  const renderStars = (rating: number): ReactElement[] => {
    return Array.from({ length: 5 }, (_, index) => (
      <i
        key={index}
        className={`fas fa-star ${
          index < rating ? "text-yellow-400" : "text-gray-600"
        }`}
      ></i>
    ));
  };

  // Convert reviews to slider items
  const reviewSliderItems = reviews.map((review, index) => ({
    id: `review-${index}`,
    content: (
      <div className="space-y-4">
        <div className="flex items-center mb-4">
          <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-[hsl(var(--space-accent))]/20 rounded-full flex items-center justify-center mr-4">
            <i className="fas fa-user text-primary"></i>
          </div>
          <div>
            <h4 className="font-semibold text-lg">{review.name}</h4>
            <p className="text-sm text-muted-foreground">{review.company}</p>
          </div>
        </div>

        <div className="flex mb-4">{renderStars(review.rating)}</div>

        <p className="text-muted-foreground mb-4 leading-relaxed italic">
          {review.text}
        </p>

        <div className="border-t border-[hsl(var(--space-secondary))] pt-4">
          <p className="text-sm text-primary font-medium">
            <i className="fas fa-project-diagram mr-2"></i>
            {review.project}
          </p>
        </div>
      </div>
    ),
    height: 280 + review.text.length * 0.5, // Dynamic height based on text length
  }));

  return (
    <section id="reviews" className="py-32 relative">
      <div className="max-w-7xl mx-auto px-8">
        <div
          ref={ref}
          className={`transition-all duration-1000 ${
            isVisible ? "animate-fade-in-up" : "opacity-0 translate-y-12"
          }`}
        >
          <h2 className="font-bold text-4xl md:text-5xl text-center mb-16 glow-text font-mono">
            Client <span className="text-primary">Reviews</span>
          </h2>

          {/* Sophisticated SVG Shadow Blend Transition Reviews Slider */}
          <InfiniteVerticalSlider
            items={reviewSliderItems}
            speed={40}
            columns={{ sm: 1, md: 2, lg: 3, xl: 4 }}
            gap={24}
            className="h-[600px] mb-16"
            pauseOnHover={true}
            blendHeight={150}
            itemClassName="hover:scale-105 review-item-fade"
            portalEffect={false}
            sophisticatedShadowEffect={true}
            vignetteIntensity={0.8}
            blendColors={{
              top: "transparent",
              bottom: "transparent",
            }}
          />

          {/* Stats Section */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center glassmorphism p-6 rounded-2xl hover-glow">
              <div className="text-3xl font-bold text-primary mb-2">500+</div>
              <div className="text-muted-foreground">Projects Completed</div>
            </div>
            <div className="text-center glassmorphism p-6 rounded-2xl hover-glow">
              <div className="text-3xl font-bold text-primary mb-2">4.9/5</div>
              <div className="text-muted-foreground">Average Rating</div>
            </div>
            <div className="text-center glassmorphism p-6 rounded-2xl hover-glow">
              <div className="text-3xl font-bold text-primary mb-2">15+</div>
              <div className="text-muted-foreground">Years Experience</div>
            </div>
            <div className="text-center glassmorphism p-6 rounded-2xl hover-glow">
              <div className="text-3xl font-bold text-primary mb-2">100%</div>
              <div className="text-muted-foreground">Client Satisfaction</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
