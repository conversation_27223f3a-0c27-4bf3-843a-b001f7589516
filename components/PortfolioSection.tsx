'use client';

import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import InfiniteVerticalSlider from '@/components/ui/InfiniteVerticalSlider';

// Portfolio/Project data
const portfolioItems = [
  {
    id: 'ecommerce-platform',
    content: (
      <div className="space-y-4">
        <div className="w-full h-40 bg-gradient-to-br from-primary/30 to-[hsl(var(--space-accent))]/30 rounded-lg flex items-center justify-center">
          <i className="fas fa-shopping-cart text-3xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-xl">E-commerce Platform</h3>
        <p className="text-muted-foreground text-sm leading-relaxed">
          A full-stack e-commerce solution with payment integration, inventory management, and admin dashboard.
        </p>
        <div className="flex flex-wrap gap-2">
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">Next.js</span>
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">Stripe</span>
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">PostgreSQL</span>
        </div>
      </div>
    ),
    height: 280,
  },
  {
    id: 'mobile-app',
    content: (
      <div className="space-y-4">
        <div className="w-full h-32 bg-gradient-to-br from-[hsl(var(--neon-accent))]/25 to-primary/25 rounded-lg flex items-center justify-center">
          <i className="fas fa-mobile-alt text-3xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-xl">Fitness Tracking App</h3>
        <p className="text-muted-foreground text-sm leading-relaxed">
          Cross-platform mobile app for fitness tracking with real-time analytics.
        </p>
        <div className="flex flex-wrap gap-2">
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">React Native</span>
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">Firebase</span>
        </div>
      </div>
    ),
    height: 240,
  },
  {
    id: 'dashboard',
    content: (
      <div className="space-y-4">
        <div className="w-full h-36 bg-gradient-to-br from-[hsl(var(--space-secondary))]/40 to-[hsl(var(--space-accent))]/40 rounded-lg flex items-center justify-center">
          <i className="fas fa-chart-bar text-3xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-xl">Analytics Dashboard</h3>
        <p className="text-muted-foreground text-sm leading-relaxed">
          Real-time data visualization dashboard with interactive charts and reports.
        </p>
        <div className="flex flex-wrap gap-2">
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">Vue.js</span>
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">D3.js</span>
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">Node.js</span>
        </div>
      </div>
    ),
    height: 260,
  },
  {
    id: 'ai-chatbot',
    content: (
      <div className="space-y-4">
        <div className="w-full h-28 bg-gradient-to-br from-primary/20 to-[hsl(var(--neon-accent))]/20 rounded-lg flex items-center justify-center">
          <i className="fas fa-robot text-3xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-xl">AI Customer Support</h3>
        <p className="text-muted-foreground text-sm leading-relaxed">
          Intelligent chatbot with natural language processing for customer support.
        </p>
        <div className="flex flex-wrap gap-2">
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">Python</span>
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">OpenAI</span>
        </div>
      </div>
    ),
    height: 220,
  },
  {
    id: 'blockchain-app',
    content: (
      <div className="space-y-4">
        <div className="w-full h-44 bg-gradient-to-br from-[hsl(var(--space-accent))]/30 to-primary/30 rounded-lg flex items-center justify-center">
          <i className="fas fa-link text-3xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-xl">DeFi Trading Platform</h3>
        <p className="text-muted-foreground text-sm leading-relaxed">
          Decentralized finance platform for cryptocurrency trading with smart contract integration.
        </p>
        <div className="flex flex-wrap gap-2">
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">Solidity</span>
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">Web3.js</span>
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">React</span>
        </div>
      </div>
    ),
    height: 300,
  },
  {
    id: 'saas-platform',
    content: (
      <div className="space-y-4">
        <div className="w-full h-32 bg-gradient-to-br from-[hsl(var(--neon-accent))]/20 to-[hsl(var(--space-secondary))]/20 rounded-lg flex items-center justify-center">
          <i className="fas fa-cloud text-3xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-xl">Project Management SaaS</h3>
        <p className="text-muted-foreground text-sm leading-relaxed">
          Cloud-based project management tool with team collaboration features.
        </p>
        <div className="flex flex-wrap gap-2">
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">Laravel</span>
          <span className="px-3 py-1 bg-primary/20 text-primary text-xs rounded-full">Vue.js</span>
        </div>
      </div>
    ),
    height: 240,
  },
];

export default function PortfolioSection() {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section id="portfolio" className="py-32 relative">
      <div className="max-w-7xl mx-auto px-8">
        <div 
          ref={ref}
          className={`transition-all duration-1000 ${
            isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-12'
          }`}
        >
          <div className="text-center mb-16">
            <h2 className="font-bold text-4xl md:text-5xl mb-6 glow-text font-mono">
              Featured <span className="text-primary">Projects</span>
            </h2>
            <p className="text-muted-foreground text-lg max-w-3xl mx-auto leading-relaxed">
              Explore a showcase of innovative projects that demonstrate cutting-edge technology 
              and creative problem-solving across various industries and platforms.
            </p>
          </div>
          
          <InfiniteVerticalSlider
            items={portfolioItems}
            speed={35}
            columns={{ sm: 1, md: 2, lg: 3, xl: 4 }}
            gap={24}
            className="h-[700px]"
            pauseOnHover={true}
            blendHeight={150}
            itemClassName="hover:scale-105"
          />
          
          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-6">
              Want to see more projects or discuss your next idea?
            </p>
            <a 
              href="#contact" 
              className="inline-flex items-center px-8 py-3 bg-primary/20 hover:bg-primary/30 text-primary border border-primary/30 rounded-full transition-all duration-300 hover-glow"
            >
              <i className="fas fa-envelope mr-2"></i>
              Get In Touch
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
