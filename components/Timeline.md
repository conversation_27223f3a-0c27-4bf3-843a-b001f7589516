# Timeline Component

An enhanced, animated timeline component for displaying chronological information with smooth animations and responsive design.

## Features

- **Animated Timeline**: Smooth scroll-triggered animations with staggered timing
- **Alternating Layout**: Cards alternate between left and right sides on desktop
- **Responsive Design**: Stacks vertically on mobile devices
- **Glassmorphism Effects**: Modern glass-like card styling with backdrop blur
- **Interactive Elements**: Hover effects and smooth transitions
- **Central Timeline Line**: Animated progress line with connecting branches
- **Customizable Content**: Flexible data structure for various use cases

## Props

### TimelineProps

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `items` | `TimelineItem[]` | Required | Array of timeline items to display |
| `title` | `string` | `"Timeline"` | Main heading for the timeline section |
| `subtitle` | `string` | `"Journey through key milestones"` | Subtitle text below the main heading |
| `className` | `string` | `""` | Additional CSS classes for the section |

### TimelineItem Interface

```typescript
interface TimelineItem {
  id: string;              // Unique identifier
  title: string;           // Main title (e.g., company name)
  subtitle?: string;       // Secondary title (e.g., job position)
  period: string;          // Time period (e.g., "2020 - 2023")
  description: string;     // Detailed description
  icon?: string;          // FontAwesome icon class (e.g., "fas fa-star")
  technologies?: string[]; // Array of technology tags
  achievements?: string[]; // Array of achievement badges
  type?: string;          // Type badge (e.g., "Full-time", "Contract")
}
```

## Usage Examples

### Basic Usage

```tsx
import Timeline, { TimelineItem } from '@/components/Timeline';

const timelineData: TimelineItem[] = [
  {
    id: "1",
    title: "Senior Developer",
    subtitle: "TechCorp",
    period: "2023 - Present",
    description: "Leading development of scalable applications...",
    icon: "fas fa-rocket",
    technologies: ["React", "Node.js", "TypeScript"],
    achievements: ["Led team of 8", "Reduced deployment time by 60%"]
  }
];

export default function MyTimeline() {
  return (
    <Timeline 
      items={timelineData}
      title="Career Journey"
      subtitle="Professional milestones and achievements"
    />
  );
}
```

### Integration with Existing Experience Section

```tsx
// Replace existing ExperienceSection in app/page.tsx
import EnhancedExperienceSection from "@/components/EnhancedExperienceSection";

// In your page component:
<EnhancedExperienceSection />
```

## Animations

The component includes several animation types:

1. **Scroll-triggered animations**: Items fade in as they come into view
2. **Staggered timing**: Each item animates with a delay for smooth progression
3. **Central line animation**: The timeline line draws from top to bottom
4. **Connection lines**: Branches from cards to the central line animate in
5. **Hover effects**: Cards lift and glow on mouse hover
6. **Background elements**: Floating orbs with subtle movement

## Responsive Behavior

- **Desktop (md+)**: Alternating left/right layout with central timeline
- **Mobile**: Stacked vertical layout with simplified design
- **Hover effects**: Reduced on mobile for better touch experience

## Accessibility

- **Reduced motion support**: Animations disabled for users who prefer reduced motion
- **Semantic HTML**: Proper heading hierarchy and structure
- **Keyboard navigation**: Interactive elements are keyboard accessible
- **Screen reader friendly**: Meaningful text content and structure

## Customization

### Styling

The component uses Tailwind CSS classes and can be customized by:

1. Modifying the `className` prop for the section
2. Updating the CSS-in-JS styles within the component
3. Overriding Tailwind classes in your global CSS

### Animation Timing

Animation delays and durations can be adjusted in the component:

```tsx
// Stagger timing for item animations
setTimeout(() => {
  setVisibleItems(prev => new Set([...prev, index]));
}, index * 200 + 300); // Adjust these values
```

### Color Scheme

The component uses CSS custom properties that can be customized:

- `--primary`: Main accent color
- `--space-dark`: Background dark color
- `--space-primary`: Background primary color

## Browser Support

- Modern browsers with CSS Grid and Flexbox support
- CSS backdrop-filter support for glassmorphism effects
- IntersectionObserver API for scroll animations

## Performance

- Optimized animations using CSS transforms
- Efficient scroll detection with IntersectionObserver
- Hydration-safe with client-side mounting checks
- Reduced motion support for accessibility

## Dependencies

- React 19+
- Next.js 15+
- Tailwind CSS 4+
- FontAwesome (for icons)
- Custom `useScrollAnimation` hook
