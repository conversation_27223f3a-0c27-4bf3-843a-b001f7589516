'use client';

import InfiniteVerticalSlider from '@/components/ui/InfiniteVerticalSlider';

// Sample data for demonstration
const sampleItems = [
  {
    id: '1',
    content: (
      <div className="space-y-3">
        <div className="w-full h-32 bg-gradient-to-br from-primary/20 to-[hsl(var(--space-accent))]/20 rounded-lg flex items-center justify-center">
          <i className="fas fa-code text-2xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-lg">Web Development</h3>
        <p className="text-muted-foreground text-sm">
          Building modern, responsive websites with cutting-edge technologies.
        </p>
      </div>
    ),
    height: 200,
  },
  {
    id: '2',
    content: (
      <div className="space-y-3">
        <div className="w-full h-40 bg-gradient-to-br from-[hsl(var(--neon-accent))]/20 to-primary/20 rounded-lg flex items-center justify-center">
          <i className="fas fa-mobile-alt text-2xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-lg">Mobile Apps</h3>
        <p className="text-muted-foreground text-sm">
          Creating intuitive mobile applications for iOS and Android platforms.
        </p>
        <div className="flex gap-2">
          <span className="px-2 py-1 bg-primary/20 text-primary text-xs rounded">React Native</span>
          <span className="px-2 py-1 bg-primary/20 text-primary text-xs rounded">Flutter</span>
        </div>
      </div>
    ),
    height: 260,
  },
  {
    id: '3',
    content: (
      <div className="space-y-3">
        <div className="w-full h-24 bg-gradient-to-br from-[hsl(var(--space-secondary))]/40 to-[hsl(var(--space-accent))]/40 rounded-lg flex items-center justify-center">
          <i className="fas fa-database text-2xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-lg">Backend Systems</h3>
        <p className="text-muted-foreground text-sm">
          Scalable server architectures and database design.
        </p>
      </div>
    ),
    height: 160,
  },
  {
    id: '4',
    content: (
      <div className="space-y-3">
        <div className="w-full h-36 bg-gradient-to-br from-primary/30 to-[hsl(var(--neon-accent))]/30 rounded-lg flex items-center justify-center">
          <i className="fas fa-paint-brush text-2xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-lg">UI/UX Design</h3>
        <p className="text-muted-foreground text-sm">
          Crafting beautiful and user-friendly interfaces that delight users.
        </p>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-primary rounded-full"></div>
          <div className="w-3 h-3 bg-[hsl(var(--neon-accent))] rounded-full"></div>
          <div className="w-3 h-3 bg-[hsl(var(--space-accent))] rounded-full"></div>
        </div>
      </div>
    ),
    height: 220,
  },
  {
    id: '5',
    content: (
      <div className="space-y-3">
        <div className="w-full h-28 bg-gradient-to-br from-[hsl(var(--space-accent))]/30 to-primary/30 rounded-lg flex items-center justify-center">
          <i className="fas fa-cloud text-2xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-lg">Cloud Solutions</h3>
        <p className="text-muted-foreground text-sm">
          Deploying and managing applications in the cloud.
        </p>
      </div>
    ),
    height: 180,
  },
  {
    id: '6',
    content: (
      <div className="space-y-3">
        <div className="w-full h-44 bg-gradient-to-br from-[hsl(var(--neon-accent))]/25 to-[hsl(var(--space-secondary))]/25 rounded-lg flex items-center justify-center">
          <i className="fas fa-chart-line text-2xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-lg">Data Analytics</h3>
        <p className="text-muted-foreground text-sm">
          Transforming raw data into actionable insights for business growth.
        </p>
        <div className="grid grid-cols-3 gap-2">
          <div className="h-2 bg-primary/40 rounded"></div>
          <div className="h-2 bg-primary/60 rounded"></div>
          <div className="h-2 bg-primary/80 rounded"></div>
        </div>
      </div>
    ),
    height: 280,
  },
  {
    id: '7',
    content: (
      <div className="space-y-3">
        <div className="w-full h-32 bg-gradient-to-br from-primary/20 to-[hsl(var(--space-accent))]/20 rounded-lg flex items-center justify-center">
          <i className="fas fa-shield-alt text-2xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-lg">Cybersecurity</h3>
        <p className="text-muted-foreground text-sm">
          Protecting digital assets with robust security measures.
        </p>
      </div>
    ),
    height: 200,
  },
  {
    id: '8',
    content: (
      <div className="space-y-3">
        <div className="w-full h-20 bg-gradient-to-br from-[hsl(var(--space-secondary))]/30 to-primary/30 rounded-lg flex items-center justify-center">
          <i className="fas fa-robot text-2xl text-primary"></i>
        </div>
        <h3 className="font-semibold text-lg">AI Integration</h3>
        <p className="text-muted-foreground text-sm">
          Implementing artificial intelligence solutions.
        </p>
      </div>
    ),
    height: 140,
  },
];

export default function InfiniteSliderDemo() {
  return (
    <section className="py-32 relative">
      <div className="max-w-6xl mx-auto px-8">
        <div className="text-center mb-16">
          <h2 className="font-bold text-4xl md:text-5xl mb-4 glow-text font-mono">
            Our <span className="text-primary">Services</span>
          </h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Discover our comprehensive range of digital solutions designed to elevate your business
          </p>
        </div>
        
        <InfiniteVerticalSlider
          items={sampleItems}
          speed={25}
          columns={{ sm: 1, md: 2, lg: 3, xl: 4 }}
          gap={20}
          className="h-[600px]"
          pauseOnHover={true}
          blendHeight={120}
        />
      </div>
    </section>
  );
}
