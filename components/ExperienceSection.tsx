'use client';

import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { useEffect, useState } from 'react';

interface Experience {
  company: string;
  position: string;
  type: string;
  period: string;
  description: string;
  icon: string;
  side: 'left' | 'right';
  technologies?: string[];
  achievements?: string[];
}

const experiences: Experience[] = [
  {
    company: "Fiverr",
    position: "Freelance Web Developer",
    type: "Level 2 Seller",
    period: "2019 – Present",
    description: "Provide full-stack web development services, specializing in custom coding for ClickFunnels, GoHighLevel, WordPress, and custom PHP/Laravel applications. Consistently maintain 4.9-star average rating.",
    icon: "fas fa-star",
    side: "left",
    technologies: ["React", "Laravel", "WordPress", "ClickFunnels"],
    achievements: ["4.9★ Rating", "500+ Projects", "Top Seller"]
  },
  {
    company: "HighLevel",
    position: "Full Stack Engineer",
    type: "Full-time",
    period: "July 2021 – September 2022",
    description: "Worked across the full stack using Node.js, TypeScript, ExpressJS, Vue.js, and Google Cloud to develop highly scalable solutions for the HighLevel marketing automation platform.",
    icon: "fas fa-layer-group",
    side: "right",
    technologies: ["Node.js", "TypeScript", "Vue.js", "Google Cloud"],
    achievements: ["Scalable Solutions", "Team Collaboration", "Cloud Architecture"]
  },
  {
    company: "Upwork",
    position: "Top Rated Freelancer",
    type: "Freelance",
    period: "2020 – Present",
    description: "Achieved Top Rated status on Upwork, delivering high-quality web development projects with expertise in React, Vue.js, Laravel, and marketing automation integrations.",
    icon: "fas fa-trophy",
    side: "left",
    technologies: ["React", "Vue.js", "Laravel", "API Integration"],
    achievements: ["Top Rated", "100% Success", "Expert Badge"]
  },
  {
    company: "Super Star BD",
    position: "Software Engineer",
    type: "Remote",
    period: "2017 – 2019",
    description: "Designed, implemented, and managed front-end & back-end development using WordPress. Developed new features, ensured high performance/availability, and implemented responsive themes/plugins.",
    icon: "fas fa-code",
    side: "right",
    technologies: ["WordPress", "PHP", "JavaScript", "MySQL"],
    achievements: ["Performance Optimization", "Custom Themes", "Plugin Development"]
  },
  {
    company: "FingerTech IT",
    position: "Team Leader (Web Development)",
    type: "Full-time",
    period: "2015 – 2017",
    description: "Led a web development team, managing projects involving WordPress, JavaScript, and Laravel. Provided technical leadership, coaching, and mentorship to ensure successful project delivery.",
    icon: "fas fa-users",
    side: "left",
    technologies: ["WordPress", "JavaScript", "Laravel", "Team Management"],
    achievements: ["Team Leadership", "Project Management", "Mentorship"]
  }
];

/**
 * Modern ExperienceSection component with simplified timeline design
 * Features glassmorphism effects and interactive animations without complex SVG
 * @returns {JSX.Element} The rendered modern experience section
 */
export default function ExperienceSection() {
  const { ref, isVisible } = useScrollAnimation();
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [selectedCard, setSelectedCard] = useState<number | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  // Ensure component is mounted on client before running animations
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    // Only run animations after component is mounted and visible
    if (isMounted && isVisible) {
      // Animate items with staggered timing
      experiences.forEach((_, index) => {
        setTimeout(() => {
          setVisibleItems(prev => new Set([...prev, index]));
        }, index * 300 + 500);
      });
    }
  }, [isVisible, isMounted]);

  /**
   * Handle card interaction for enhanced user experience
   * @param {number} index - The index of the experience card
   */
  const handleCardClick = (index: number) => {
    setSelectedCard(selectedCard === index ? null : index);
  };

  // Prevent hydration mismatch by showing static content until mounted
  if (!isMounted) {
    return (
      <section id="experience" className="py-20 md:py-32 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 md:px-8 relative z-10">
          <div className="opacity-0">
            {/* Section header */}
            <div className="text-center mb-20 md:mb-28">
              <div className="inline-block relative">
                <h2 className="font-bold text-4xl md:text-5xl lg:text-6xl mb-4 bg-gradient-to-r from-primary via-blue-500 to-purple-500 bg-clip-text text-transparent">
                  Professional Journey
                </h2>
              </div>
              <p className="text-lg md:text-xl text-muted-foreground mt-6 max-w-2xl mx-auto leading-relaxed">
                Explore my career progression through innovative projects and meaningful contributions
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="experience" className="py-20 md:py-32 relative overflow-hidden">
      {/* Modern background with floating elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-blue-500/5"></div>
        
        {/* Floating orbs */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-full blur-xl animate-float-slow"></div>
        <div className="absolute bottom-32 right-16 w-48 h-48 bg-gradient-to-br from-blue-500/15 to-cyan-500/15 rounded-full blur-2xl animate-float-medium"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-gradient-to-br from-purple-500/25 to-pink-500/25 rounded-full blur-lg animate-float-fast"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 md:px-8 relative z-10">
        <div 
          ref={ref}
          className={`transition-all duration-1000 ${
            isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-12'
          }`}
        >
          {/* Section header */}
          <div className="text-center mb-20 md:mb-28">
            <div className="inline-block relative">
              <h2 className="font-bold text-4xl md:text-5xl lg:text-6xl mb-4 bg-gradient-to-r from-primary via-blue-500 to-purple-500 bg-clip-text text-transparent">
                Professional Journey
              </h2>
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-primary to-blue-500 rounded-full animate-expand"></div>
            </div>
            <p className="text-lg md:text-xl text-muted-foreground mt-6 max-w-2xl mx-auto leading-relaxed">
              Explore my career progression through innovative projects and meaningful contributions
            </p>
          </div>
          
          {/* Simplified timeline */}
          <div className="relative">
            {/* Experience cards */}
            <div className="space-y-12 md:space-y-12 relative z-10">
              {experiences.map((exp, index) => {
                return (
                  <div 
                    key={index} 
                    className={`relative transition-all duration-700 ${
                      visibleItems.has(index) 
                        ? 'animate-slide-in-modern' 
                        : 'opacity-0 translate-y-16'
                    }`}
                    style={{ animationDelay: `${index * 300}ms` }}
                  >
                    {/* Experience card */}
                    <div className={`ml-16 md:ml-0`}>
                      <div 
                        className={`glass-card group cursor-pointer transition-all duration-500 ${
                          hoveredCard === index ? 'hovered' : ''
                        } ${selectedCard === index ? 'selected' : ''}`}
                        onMouseEnter={() => setHoveredCard(index)}
                        onMouseLeave={() => setHoveredCard(null)}
                        onClick={() => handleCardClick(index)}
                      >
                        {/* Card content */}
                        <div className="relative z-10">
                          {/* Header with icon and company */}
                          <div className={`flex items-center mb-6 ${
                            exp.side === 'left' ? 'justify-start md:justify-end' : 'justify-start'
                          }`}>
                            <div className={`${
                              exp.side === 'left' ? 'order-1 mr-4 md:order-2 md:mr-0 md:ml-4' : 'order-1 mr-4'
                            }`}>
                              <div className="relative">
                                <div className="absolute inset-0 bg-primary/30 rounded-2xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                                <div className="relative w-16 h-16 bg-gradient-to-br from-primary/20 to-blue-500/20 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/10">
                                  <i className={`${exp.icon} text-2xl text-primary group-hover:scale-110 transition-transform duration-300`}></i>
                                </div>
                              </div>
                            </div>
                            
                            <div className={`${
                              exp.side === 'left' ? 'order-2 md:order-1 md:text-right' : 'order-2'
                            }`}>
                              <h3 className="font-bold text-xl md:text-2xl text-primary mb-2 group-hover:text-primary/90 transition-colors duration-300">
                                {exp.company}
                              </h3>
                              <span className="inline-block px-3 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full border border-primary/20">
                                {exp.type}
                              </span>
                            </div>
                          </div>
                          
                          {/* Position and period */}
                          <div className={`mb-4 ${
                            exp.side === 'left' ? 'text-left md:text-right' : 'text-left'
                          }`}>
                            <h4 className="font-semibold text-lg md:text-xl mb-2 group-hover:text-primary/90 transition-colors duration-300">
                              {exp.position}
                            </h4>
                            <p className="text-sm font-medium text-blue-400 bg-blue-400/10 px-3 py-1 rounded-lg inline-block border border-blue-400/20">
                              {exp.period}
                            </p>
                          </div>
                          
                          {/* Description */}
                          <p className={`text-muted-foreground leading-relaxed mb-6 group-hover:text-foreground/90 transition-colors duration-300 ${
                            exp.side === 'left' ? 'text-left md:text-right' : 'text-left'
                          }`}>
                            {exp.description}
                          </p>
                          
                          {/* Technologies */}
                          {exp.technologies && (
                            <div className={`mb-4 ${
                              exp.side === 'left' ? 'text-left md:text-right' : 'text-left'
                            }`}>
                              <div className={`flex flex-wrap gap-2 ${
                                exp.side === 'left' ? 'justify-start md:justify-end' : 'justify-start'
                              }`}>
                                {exp.technologies.map((tech, techIndex) => (
                                  <span 
                                    key={techIndex}
                                    className="px-3 py-1 text-xs bg-gradient-to-r from-purple-500/10 to-pink-500/10 text-purple-300 rounded-full border border-purple-500/20 hover:border-purple-500/40 transition-colors duration-300"
                                  >
                                    {tech}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                          
                          {/* Achievements - expandable */}
                          <div className={`achievements-section transition-all duration-500 overflow-hidden ${
                            selectedCard === index ? 'max-h-40 opacity-100' : 'max-h-0 opacity-0'
                          }`}>
                            {exp.achievements && (
                              <div className={`pt-4 border-t border-white/10 ${
                                exp.side === 'left' ? 'text-left md:text-right' : 'text-left'
                              }`}>
                                <h5 className="text-sm font-semibold text-primary mb-3">Key Achievements:</h5>
                                <div className={`flex flex-wrap gap-2 ${
                                  exp.side === 'left' ? 'justify-start md:justify-end' : 'justify-start'
                                }`}>
                                  {exp.achievements.map((achievement, achIndex) => (
                                    <span 
                                      key={achIndex}
                                      className="px-3 py-1 text-xs bg-gradient-to-r from-green-500/10 to-emerald-500/10 text-green-300 rounded-full border border-green-500/20"
                                    >
                                      {achievement}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                          
                          {/* Expand indicator */}
                          <div className={`mt-4 flex items-center justify-center ${
                            exp.side === 'left' ? 'md:justify-end' : 'md:justify-start'
                          }`}>
                            <div className="flex items-center text-xs text-muted-foreground group-hover:text-primary transition-colors duration-300">
                              <span className="mr-1">
                                {selectedCard === index ? 'Click to collapse' : 'Click to expand'}
                              </span>
                              <i className={`fas fa-chevron-${selectedCard === index ? 'up' : 'down'} transition-transform duration-300`}></i>
                            </div>
                          </div>
                        </div>
                        
                        {/* Decorative elements */}
                        <div className="absolute top-4 right-4 w-2 h-2 bg-primary/40 rounded-full animate-twinkle-slow"></div>
                        <div className="absolute bottom-4 left-4 w-1.5 h-1.5 bg-blue-400/40 rounded-full animate-twinkle-medium"></div>
                        
                        {/* Hover effect overlay */}
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-blue-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Modern styles and animations */}
      <style jsx>{`
        .glass-card {
          background: linear-gradient(135deg, 
            rgba(255, 255, 255, 0.1) 0%, 
            rgba(255, 255, 255, 0.05) 50%, 
            rgba(255, 255, 255, 0.1) 100%
          );
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 24px;
          padding: 2rem;
          position: relative;
          overflow: hidden;
          transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 
            0 8px 32px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        .glass-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(233, 69, 96, 0.1),
            transparent
          );
          transition: left 0.8s ease;
        }
        
        .glass-card.hovered {
          transform: translateY(-12px) scale(1.02);
          border-color: rgba(233, 69, 96, 0.3);
          box-shadow: 
            0 24px 48px rgba(0, 0, 0, 0.4),
            0 0 40px rgba(233, 69, 96, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }
        
        .glass-card.hovered::before {
          left: 100%;
        }
        
        .glass-card.selected {
          border-color: rgba(233, 69, 96, 0.5);
          box-shadow: 
            0 20px 40px rgba(0, 0, 0, 0.4),
            0 0 50px rgba(233, 69, 96, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }
        
        /* Animations */
        .animate-slide-in-modern {
          animation: slideInModern 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
        }
        
        .animate-float-slow {
          animation: floatSlow 8s ease-in-out infinite;
        }
        
        .animate-float-medium {
          animation: floatMedium 6s ease-in-out infinite;
          animation-delay: 2s;
        }
        
        .animate-float-fast {
          animation: floatFast 4s ease-in-out infinite;
          animation-delay: 1s;
        }
        
        .animate-twinkle-slow {
          animation: twinkle 4s ease-in-out infinite;
        }
        
        .animate-twinkle-medium {
          animation: twinkle 3s ease-in-out infinite;
          animation-delay: 1s;
        }
        
        .animate-expand {
          animation: expand 1s ease-out 0.5s both;
        }
        
        @keyframes slideInModern {
          0% {
            opacity: 0;
            transform: translateY(60px) scale(0.9);
          }
          100% {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
        
        @keyframes floatSlow {
          0%, 100% {
            transform: translateY(0px) translateX(0px) rotate(0deg);
          }
          33% {
            transform: translateY(-20px) translateX(10px) rotate(120deg);
          }
          66% {
            transform: translateY(10px) translateX(-10px) rotate(240deg);
          }
        }
        
        @keyframes floatMedium {
          0%, 100% {
            transform: translateY(0px) translateX(0px) rotate(0deg);
          }
          50% {
            transform: translateY(-30px) translateX(15px) rotate(180deg);
          }
        }
        
        @keyframes floatFast {
          0%, 100% {
            transform: translateY(0px) translateX(0px) scale(1);
          }
          25% {
            transform: translateY(-15px) translateX(8px) scale(1.1);
          }
          75% {
            transform: translateY(8px) translateX(-8px) scale(0.9);
          }
        }
        
        @keyframes twinkle {
          0%, 100% {
            opacity: 0.3;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.5);
          }
        }
        
        @keyframes expand {
          0% {
            width: 0;
            opacity: 0;
          }
          100% {
            width: 6rem;
            opacity: 1;
          }
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
          .glass-card {
            padding: 1.5rem;
            border-radius: 20px;
          }
          
          .glass-card.hovered {
            transform: translateY(-8px) scale(1.01);
          }
        }
        
        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          .animate-float-slow,
          .animate-float-medium,
          .animate-float-fast,
          .animate-twinkle-slow,
          .animate-twinkle-medium {
            animation: none !important;
          }
          
          .glass-card.hovered {
            transform: none;
          }
        }
      `}</style>
    </section>
  );
}
