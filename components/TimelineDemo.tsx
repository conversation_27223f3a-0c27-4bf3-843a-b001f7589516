'use client';

import Timeline, { TimelineItem } from './Timeline';

// Sample timeline data for demonstration
const sampleTimelineData: TimelineItem[] = [
  {
    id: "1",
    title: "Senior Full Stack Developer",
    subtitle: "TechCorp Solutions",
    period: "2023 - Present",
    type: "Full-time",
    description: "Leading development of scalable web applications using modern technologies. Architecting microservices and implementing CI/CD pipelines for improved deployment efficiency.",
    icon: "fas fa-rocket",
    technologies: ["React", "Node.js", "TypeScript", "AWS", "Docker"],
    achievements: ["Led team of 8 developers", "Reduced deployment time by 60%", "Implemented microservices architecture"]
  },
  {
    id: "2",
    title: "Full Stack Engineer",
    subtitle: "InnovateLabs",
    period: "2021 - 2023",
    type: "Full-time",
    description: "Developed and maintained multiple client-facing applications. Collaborated with cross-functional teams to deliver high-quality software solutions on time and within budget.",
    icon: "fas fa-code",
    technologies: ["Vue.js", "Python", "Django", "PostgreSQL", "Redis"],
    achievements: ["Delivered 15+ projects", "99.9% uptime", "Client satisfaction: 4.8/5"]
  },
  {
    id: "3",
    title: "Frontend Developer",
    subtitle: "StartupXYZ",
    period: "2020 - 2021",
    type: "Contract",
    description: "Built responsive and interactive user interfaces for a fast-growing startup. Focused on performance optimization and user experience improvements.",
    icon: "fas fa-paint-brush",
    technologies: ["React", "JavaScript", "SASS", "Webpack", "Jest"],
    achievements: ["Improved page load speed by 40%", "Increased user engagement by 25%", "Mobile-first responsive design"]
  },
  {
    id: "4",
    title: "Junior Web Developer",
    subtitle: "WebStudio Pro",
    period: "2019 - 2020",
    type: "Full-time",
    description: "Started career in web development, learning modern frameworks and best practices. Contributed to various client projects and internal tools development.",
    icon: "fas fa-seedling",
    technologies: ["HTML", "CSS", "JavaScript", "jQuery", "PHP"],
    achievements: ["Completed 20+ client projects", "Learned 5 new technologies", "Mentored 2 interns"]
  },
  {
    id: "5",
    title: "Computer Science Degree",
    subtitle: "University of Technology",
    period: "2015 - 2019",
    type: "Education",
    description: "Bachelor's degree in Computer Science with focus on software engineering and web technologies. Graduated with honors and completed several notable projects.",
    icon: "fas fa-graduation-cap",
    technologies: ["Java", "C++", "Python", "SQL", "Data Structures"],
    achievements: ["Graduated Magna Cum Laude", "Dean's List 6 semesters", "Led final year project team"]
  }
];

/**
 * Demo component showcasing the Timeline component with sample data
 */
export default function TimelineDemo() {
  return (
    <Timeline 
      items={sampleTimelineData}
      title="Professional Journey"
      subtitle="A timeline of career milestones and achievements"
      className="bg-gradient-to-br from-[hsl(var(--space-dark))] to-[hsl(var(--space-primary))]"
    />
  );
}
