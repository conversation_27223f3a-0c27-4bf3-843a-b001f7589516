'use client';

import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { useEffect, useState } from 'react';

export interface TimelineItem {
  id: string;
  title: string;
  subtitle?: string;
  period: string;
  description: string;
  icon?: string;
  technologies?: string[];
  achievements?: string[];
  type?: string;
}

interface TimelineProps {
  items: TimelineItem[];
  title?: string;
  subtitle?: string;
  className?: string;
}

/**
 * Enhanced Timeline component with smooth animations and responsive design
 * Features alternating left/right layout with glassmorphism effects
 */
export default function Timeline({
  items,
  title = "Timeline",
  subtitle = "Journey through key milestones",
  className = ""
}: TimelineProps) {
  const { ref, isVisible } = useScrollAnimation();
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  // Ensure component is mounted on client before running animations
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    // Only run animations after component is mounted and visible
    if (isMounted && isVisible) {
      // Animate items with staggered timing
      items.forEach((_, index) => {
        setTimeout(() => {
          setVisibleItems(prev => new Set([...prev, index]));
        }, index * 200 + 300);
      });
    }
  }, [isVisible, isMounted, items]);

  // Prevent hydration mismatch by showing static content until mounted
  if (!isMounted) {
    return (
      <section className={`py-20 md:py-32 relative overflow-hidden ${className}`}>
        <div className="max-w-7xl mx-auto px-4 md:px-8 relative z-10">
          <div className="opacity-0">
            <div className="text-center mb-20 md:mb-28">
              <h2 className="font-bold text-4xl md:text-5xl lg:text-6xl mb-4 bg-gradient-to-r from-primary via-blue-500 to-purple-500 bg-clip-text text-transparent">
                {title}
              </h2>
              <p className="text-lg md:text-xl text-muted-foreground mt-6 max-w-2xl mx-auto leading-relaxed">
                {subtitle}
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={`py-20 md:py-32 relative overflow-hidden ${className}`}>
      {/* Background effects */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-blue-500/5"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-full blur-xl animate-float-slow"></div>
        <div className="absolute bottom-32 right-16 w-48 h-48 bg-gradient-to-br from-blue-500/15 to-cyan-500/15 rounded-full blur-2xl animate-float-medium"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-gradient-to-br from-purple-500/25 to-pink-500/25 rounded-full blur-lg animate-float-fast"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 md:px-8 relative z-10">
        <div
          ref={ref}
          className={`transition-all duration-1000 ${
            isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-12'
          }`}
        >
          {/* Section header */}
          <div className="text-center mb-20 md:mb-28">
            <div className="inline-block relative">
              <h2 className="font-bold text-4xl md:text-5xl lg:text-6xl mb-4 bg-gradient-to-r from-primary via-blue-500 to-purple-500 bg-clip-text text-transparent">
                {title}
              </h2>
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-primary to-blue-500 rounded-full animate-expand"></div>
            </div>
            <p className="text-lg md:text-xl text-muted-foreground mt-6 max-w-2xl mx-auto leading-relaxed">
              {subtitle}
            </p>
          </div>

          {/* Timeline container */}
          <div className="relative">
            {/* Central timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-primary/30 via-primary/60 to-primary/30 rounded-full hidden md:block">
              {/* Animated progress line */}
              <div
                className={`absolute top-0 left-0 w-full bg-gradient-to-b from-primary to-blue-500 rounded-full transition-all duration-2000 ease-out ${
                  isVisible ? 'h-full' : 'h-0'
                }`}
                style={{ transitionDelay: '500ms' }}
              ></div>
            </div>

            {/* Timeline items */}
            <div className="space-y-16 md:space-y-20">
              {items.map((item, index) => {
                const isLeft = index % 2 === 0;
                const isVisible = visibleItems.has(index);

                return (
                  <div
                    key={item.id}
                    className={`relative transition-all duration-700 ${
                      isVisible
                        ? 'animate-timeline-slide-in'
                        : 'opacity-0'
                    }`}
                    style={{
                      animationDelay: `${index * 200 + 800}ms`,
                      transform: isVisible ? 'none' : `translateX(${isLeft ? '-60px' : '60px'}) translateY(20px)`
                    }}
                  >
                    {/* Timeline node (center point) */}
                    <div className="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2 top-1/2 z-20 hidden md:block">
                      <div className="relative">
                        <div className="absolute inset-0 bg-primary/30 rounded-full blur-lg animate-pulse-slow"></div>
                        <div className="relative w-6 h-6 bg-gradient-to-br from-primary to-blue-500 rounded-full border-4 border-background shadow-lg">
                          <div className="absolute inset-1 bg-white/20 rounded-full animate-twinkle"></div>
                        </div>
                      </div>
                    </div>

                    {/* Content card */}
                    <div className={`md:w-5/12 ${isLeft ? 'md:mr-auto md:pr-16' : 'md:ml-auto md:pl-16'}`}>
                      <div
                        className={`timeline-card group cursor-pointer transition-all duration-500 ${
                          hoveredCard === index ? 'hovered' : ''
                        }`}
                        onMouseEnter={() => setHoveredCard(index)}
                        onMouseLeave={() => setHoveredCard(null)}
                      >
                        {/* Connection line to center (desktop only) */}
                        <div className={`absolute top-1/2 transform -translate-y-1/2 w-16 h-0.5 bg-gradient-to-r from-primary/40 to-transparent hidden md:block ${
                          isLeft ? 'right-0 translate-x-full' : 'left-0 -translate-x-full rotate-180'
                        }`}>
                          <div className={`absolute inset-0 bg-gradient-to-r from-primary to-blue-500 transition-all duration-700 ${
                            isVisible ? 'scale-x-100' : 'scale-x-0'
                          }`} style={{ transformOrigin: isLeft ? 'left' : 'right', transitionDelay: `${index * 200 + 1200}ms` }}></div>
                        </div>

                        {/* Card content */}
                        <div className="relative z-10 p-6 md:p-8">
                          {/* Header */}
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <h3 className="font-bold text-xl md:text-2xl text-primary mb-2 group-hover:text-primary/90 transition-colors duration-300">
                                {item.title}
                              </h3>
                              {item.subtitle && (
                                <h4 className="font-semibold text-lg text-foreground/90 mb-2">
                                  {item.subtitle}
                                </h4>
                              )}
                              <div className="flex flex-wrap gap-2 mb-4">
                                <span className="inline-block px-3 py-1 text-xs font-medium bg-blue-400/10 text-blue-400 rounded-full border border-blue-400/20">
                                  {item.period}
                                </span>
                                {item.type && (
                                  <span className="inline-block px-3 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full border border-primary/20">
                                    {item.type}
                                  </span>
                                )}
                              </div>
                            </div>

                            {/* Icon */}
                            {item.icon && (
                              <div className="ml-4">
                                <div className="relative">
                                  <div className="absolute inset-0 bg-primary/30 rounded-2xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                                  <div className="relative w-12 h-12 bg-gradient-to-br from-primary/20 to-blue-500/20 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/10">
                                    <i className={`${item.icon} text-lg text-primary group-hover:scale-110 transition-transform duration-300`}></i>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Description */}
                          <p className="text-muted-foreground leading-relaxed mb-6 group-hover:text-foreground/90 transition-colors duration-300">
                            {item.description}
                          </p>

                          {/* Technologies */}
                          {item.technologies && item.technologies.length > 0 && (
                            <div className="mb-4">
                              <div className="flex flex-wrap gap-2">
                                {item.technologies.map((tech, techIndex) => (
                                  <span
                                    key={techIndex}
                                    className="px-3 py-1 text-xs bg-gradient-to-r from-purple-500/10 to-pink-500/10 text-purple-300 rounded-full border border-purple-500/20 hover:border-purple-500/40 transition-colors duration-300"
                                  >
                                    {tech}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Achievements */}
                          {item.achievements && item.achievements.length > 0 && (
                            <div className="pt-4 border-t border-white/10">
                              <h5 className="text-sm font-semibold text-primary mb-3">Key Achievements:</h5>
                              <div className="flex flex-wrap gap-2">
                                {item.achievements.map((achievement, achIndex) => (
                                  <span
                                    key={achIndex}
                                    className="px-3 py-1 text-xs bg-gradient-to-r from-green-500/10 to-emerald-500/10 text-green-300 rounded-full border border-green-500/20"
                                  >
                                    {achievement}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Decorative elements */}
                        <div className="absolute top-4 right-4 w-2 h-2 bg-primary/40 rounded-full animate-twinkle-slow"></div>
                        <div className="absolute bottom-4 left-4 w-1.5 h-1.5 bg-blue-400/40 rounded-full animate-twinkle-medium"></div>

                        {/* Hover effect overlay */}
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-blue-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Timeline styles */}
      <style jsx>{`
        .timeline-card {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            rgba(255, 255, 255, 0.1) 100%
          );
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 24px;
          position: relative;
          overflow: hidden;
          transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .timeline-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(233, 69, 96, 0.1),
            transparent
          );
          transition: left 0.8s ease;
        }

        .timeline-card.hovered {
          transform: translateY(-8px) scale(1.02);
          border-color: rgba(233, 69, 96, 0.3);
          box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.4),
            0 0 30px rgba(233, 69, 96, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .timeline-card.hovered::before {
          left: 100%;
        }

        /* Animations */
        .animate-timeline-slide-in {
          animation: timelineSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
        }

        .animate-expand {
          animation: expand 1s ease-out 0.5s both;
        }

        @keyframes timelineSlideIn {
          0% {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
          }
          100% {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @keyframes expand {
          0% {
            width: 0;
            opacity: 0;
          }
          100% {
            width: 6rem;
            opacity: 1;
          }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .timeline-card {
            border-radius: 20px;
          }

          .timeline-card.hovered {
            transform: translateY(-4px) scale(1.01);
          }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          .animate-float-slow,
          .animate-float-medium,
          .animate-float-fast,
          .animate-twinkle-slow,
          .animate-twinkle-medium,
          .animate-pulse-slow,
          .animate-twinkle {
            animation: none !important;
          }

          .timeline-card.hovered {
            transform: none;
          }
        }
      `}</style>
    </section>
  );
}