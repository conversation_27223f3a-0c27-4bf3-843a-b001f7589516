'use client';

import { useScrollAnimation } from '@/hooks/useScrollAnimation';

export default function ThankYouSection() {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section className="py-32 relative">
      <div className="max-w-4xl mx-auto px-8 text-center">
        <div 
          ref={ref}
          className={`transition-all duration-1000 ${
            isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-12'
          }`}
        >
          <div className="glassmorphism p-12 rounded-3xl hover-glow">
            <div className="mb-8">
              <i className="fas fa-heart text-6xl text-primary mb-4 animate-pulse"></i>
            </div>
            
            <h2 className="font-bold text-4xl md:text-5xl mb-6 glow-text font-mono">
              Thank <span className="text-primary">You!</span>
            </h2>
            
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Thank you for taking the time to explore my portfolio. I&apos;m passionate about creating 
              exceptional digital experiences and would love to help bring your next project to life.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <a 
                href="mailto:<EMAIL>"
                className="bg-primary hover:bg-primary/80 px-8 py-4 rounded-full font-semibold transition-all duration-300 hover:scale-105"
              >
                <i className="fas fa-envelope mr-2"></i>Start a Conversation
              </a>
              
              <a 
                href="https://www.linkedin.com/in/ain477/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="border border-primary text-primary hover:bg-primary hover:text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 hover:scale-105"
              >
                <i className="fab fa-linkedin mr-2"></i>Connect on LinkedIn
              </a>
            </div>
            
            <div className="mt-12 pt-8 border-t border-[hsl(var(--space-secondary))]">
              <p className="text-muted-foreground">
                <i className="fas fa-quote-left mr-2 text-primary"></i>
                &ldquo;The best way to predict the future is to create it.&rdquo;
                <i className="fas fa-quote-right ml-2 text-primary"></i>
              </p>
              <p className="text-sm text-muted-foreground mt-2">- Peter Drucker</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
