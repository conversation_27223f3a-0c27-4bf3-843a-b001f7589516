'use client';

import Timeline, { TimelineItem } from './Timeline';

// Convert existing experience data to Timeline format
const experienceTimelineData: TimelineItem[] = [
  {
    id: "fiverr",
    title: "Fiverr",
    subtitle: "Freelance Web Developer",
    period: "2019 – Present",
    type: "Level 2 Seller",
    description: "Provide full-stack web development services, specializing in custom coding for ClickFunnels, GoHighLevel, WordPress, and custom PHP/Laravel applications. Consistently maintain 4.9-star average rating.",
    icon: "fas fa-star",
    technologies: ["React", "Laravel", "WordPress", "ClickFunnels"],
    achievements: ["4.9★ Rating", "500+ Projects", "Top Seller"]
  },
  {
    id: "highlevel",
    title: "HighLevel",
    subtitle: "Full Stack Engineer",
    period: "July 2021 – September 2022",
    type: "Full-time",
    description: "Worked across the full stack using Node.js, TypeScript, ExpressJS, Vue.js, and Google Cloud to develop highly scalable solutions for the HighLevel marketing automation platform.",
    icon: "fas fa-layer-group",
    technologies: ["Node.js", "TypeScript", "Vue.js", "Google Cloud"],
    achievements: ["Scalable Solutions", "Team Collaboration", "Cloud Architecture"]
  },
  {
    id: "upwork",
    title: "Upwork",
    subtitle: "Top Rated Freelancer",
    period: "2020 – Present",
    type: "Freelance",
    description: "Achieved Top Rated status on Upwork, delivering high-quality web development projects with expertise in React, Vue.js, Laravel, and marketing automation integrations.",
    icon: "fas fa-trophy",
    technologies: ["React", "Vue.js", "Laravel", "API Integration"],
    achievements: ["Top Rated", "100% Success", "Expert Badge"]
  },
  {
    id: "superstar",
    title: "Super Star BD",
    subtitle: "Software Engineer",
    period: "2017 – 2019",
    type: "Remote",
    description: "Designed, implemented, and managed front-end & back-end development using WordPress. Developed new features, ensured high performance/availability, and implemented responsive themes/plugins.",
    icon: "fas fa-code",
    technologies: ["WordPress", "PHP", "JavaScript", "MySQL"],
    achievements: ["Performance Optimization", "Custom Themes", "Plugin Development"]
  },
  {
    id: "fingertech",
    title: "FingerTech IT",
    subtitle: "Team Leader (Web Development)",
    period: "2015 – 2017",
    type: "Full-time",
    description: "Led a web development team, managing projects involving WordPress, JavaScript, and Laravel. Provided technical leadership, coaching, and mentorship to ensure successful project delivery.",
    icon: "fas fa-users",
    technologies: ["WordPress", "JavaScript", "Laravel", "Team Management"],
    achievements: ["Team Leadership", "Project Management", "Mentorship"]
  }
];

/**
 * Enhanced Experience Section using the new Timeline component
 * Replaces the existing experience section with modern animated timeline
 */
export default function EnhancedExperienceSection() {
  return (
    <Timeline 
      items={experienceTimelineData}
      title="Professional Journey"
      subtitle="Explore my career progression through innovative projects and meaningful contributions"
      className=""
    />
  );
}
