# Al-Amin <PERSON>b

**Email:** [<EMAIL>](mailto:<EMAIL>)
**LinkedIn:** [linkedin.com/in/ain477](https://www.linkedin.com/in/ain477)
**Fiverr:** [fiverr.com/aincoder](https://www.fiverr.com/aincoder)

---

## Professional Summary

Results-oriented Full Stack Developer with extensive experience in web application architecture, marketing automation, and CRM platform customization. Adept at leveraging modern JavaScript frameworks like VueJS and ReactJS, backend technologies such as Node.js, ExpressJS, and Firebase, and CMS platforms including WordPress and Shopify. Demonstrated ability to deliver scalable solutions through both freelance and in-house roles, and passionate about delivering business value through clean code, collaboration, and automation.

---

## Professional Experience

### **HighLevel – Remote**

**Full Stack Engineer**
_Jul 2021 – Sep 2022_
Enhanced the functionality of the calendar module and delivered new scheduling features for a leading marketing automation platform.

- Developed features using Vue.js, Vuex, TypeScript, ExpressJS, and Firebase
- Improved calendar performance and reliability
- Collaborated with backend and product teams for end-to-end delivery

### **Fiverr – Remote**

**Freelance Web Developer (Level 2 Seller)**
_2019 – Present_
Delivered 100+ successful freelance projects with a focus on marketing funnels, web applications, and CMS customization.

- Specialized in HighLevel, ClickFunnels, WordPress, and Laravel
- Built landing pages, dashboards, and automation workflows
- Maintained a 4.9+ star rating with consistent client satisfaction

### **Upwork – Remote**

**Premium Web Developer**
_2010 – 2016_
Recognized as a premium freelancer for delivering custom full-stack solutions across diverse sectors.

- Developed applications using PHP, Laravel, VueJS, and WordPress
- Created frontend interfaces and custom CMS solutions
- Achieved high client retention and performance score

### **ClikView – Remote**

**Senior Software Engineer**
_Oct 2022 – Dec 2023_
Contributed to the development of a video-sharing platform similar to YouTube, built with custom PHP.

- Focused on video processing: uploading, resolution conversion, and playback optimization
- Improved performance and user experience in the video module

### **autoPatient.co – Remote**

**Software Engineer**
_Jan 2024 – Dec 2024_
Worked on a healthcare-focused marketing SaaS platform, contributing across multiple features.

- Developed custom booking widgets and landing pages
- Integrated REST APIs and real-time communication with Socket.io
- Used AdonisJS for backend services and scalable architecture

### **Super Star BD – Remote**

**Software Engineer**
_2017 – 2019_
Built and launched the company's official website using WordPress.

- Customized themes and plugins
- Delivered a clean, SEO-optimized, and mobile-responsive site

### **FingerTech IT – Dhaka**

**Software Engineer**
_2015 – 2017_
Contributed to landing pages and sales funnels for marketing campaigns.

- Used ReactJS and WordPress for rapid frontend development
- Supported design-to-deployment lifecycle for various client projects

### **Next Generation IT – Faridpur, Dhaka**

**Intern Software Engineer**
_2013 – 2014_
Worked on internal tools and client projects using Laravel and WordPress.

- Supported backend logic and CMS customization
- Gained hands-on experience with PHP development workflows

---

## Education

**Mohim Institution – Faridpur, Dhaka**
_Secondary School Certificate (SSC)_
_2009 – 2011_

---
