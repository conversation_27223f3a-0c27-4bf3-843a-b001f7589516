import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Providers from "@/components/Providers";
import { Toaster } from "@/components/ui/toaster";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Al-Amin Islam Nerob - Full Stack Developer",
  description: "Full-Stack Developer | Vue, React, Next.js, Laravel | TypeScript, Node.js, PHP | AI & Chatbots | Marketing Automation (Zapier, n8n, HighLevel)",
  keywords: "Full Stack Developer, React, Next.js, Vue.js, Laravel, TypeScript, Node.js, PHP, AI Chatbots, Marketing Automation",
  authors: [{ name: "Al-<PERSON><PERSON>" }],
  openGraph: {
    title: "Al-Am<PERSON> - Full Stack Developer",
    description: "Full-Stack Developer specializing in React, Vue.js, Next.js, Laravel, and marketing automation",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        <Providers>
          <Toaster />
          {children}
        </Providers>
      </body>
    </html>
  );
}
