@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

:root {
  /* #0A0A1B */
  --muted: oklch(0.97 0.001 106.424);
  --muted-foreground: oklch(0.553 0.013 58.071);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.147 0.004 49.25);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.147 0.004 49.25);
  --border: oklch(0.923 0.003 48.717);
  --input: oklch(0.923 0.003 48.717);
  --primary: oklch(0.216 0.006 56.043); /* #E94560 */
  --primary-foreground: oklch(0.985 0.001 106.423);
  --secondary: oklch(0.97 0.001 106.424);
  --secondary-foreground: oklch(0.216 0.006 56.043);
  --accent: oklch(0.97 0.001 106.424);
  --accent-foreground: oklch(0.216 0.006 56.043);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: 0 0% 98%;
  --ring: oklch(0.709 0.01 56.259);
  --radius: 0.625rem;

  /* Custom space theme colors */
  --space-dark: 236 44% 5%; /* #0A0A1B */
  --space-primary: 236 43% 14%; /* #1A1A2E */
  --space-secondary: 221 42% 19%; /* #16213E */
  --space-accent: 207 63% 22%; /* #0F3460 */
  --neon-accent: 345 83% 47%; /* #E94560 */
  --text-muted: 225 14% 70%; /* #B8BCC8 */
  --background: oklch(1 0 0);
  --foreground: oklch(0.147 0.004 49.25);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0.001 106.423);
  --sidebar-foreground: oklch(0.147 0.004 49.25);
  --sidebar-primary: oklch(0.216 0.006 56.043);
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.97 0.001 106.424);
  --sidebar-accent-foreground: oklch(0.216 0.006 56.043);
  --sidebar-border: oklch(0.923 0.003 48.717);
  --sidebar-ring: oklch(0.709 0.01 56.259);
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-primary: hsl(var(--primary));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

.dark {
  --background: oklch(0.147 0.004 49.25); /* #0A0A1B */
  --foreground: oklch(0.985 0.001 106.423);
  --muted: oklch(0.268 0.007 34.298);
  --muted-foreground: oklch(0.709 0.01 56.259);
  --popover: oklch(0.216 0.006 56.043);
  --popover-foreground: oklch(0.985 0.001 106.423);
  --card: oklch(0.216 0.006 56.043);
  --card-foreground: oklch(0.985 0.001 106.423);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --primary: oklch(0.923 0.003 48.717); /* #E94560 */
  --primary-foreground: oklch(0.216 0.006 56.043);
  --secondary: oklch(0.268 0.007 34.298);
  --secondary-foreground: oklch(0.985 0.001 106.423);
  --accent: oklch(0.268 0.007 34.298);
  --accent-foreground: oklch(0.985 0.001 106.423);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: 0 0% 98%;
  --ring: oklch(0.553 0.013 58.071);
  --radius: 0.5rem;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.216 0.006 56.043);
  --sidebar-foreground: oklch(0.985 0.001 106.423);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.268 0.007 34.298);
  --sidebar-accent-foreground: oklch(0.985 0.001 106.423);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.553 0.013 58.071);
}

* {
  border-color: hsl(var(--border));
}

body {
  color: hsl(var(--foreground));
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  min-height: 100vh;
  font-feature-settings: "rlig" 1, "calt" 1;
}

html {
  scroll-behavior: smooth;
}

.glassmorphism {
  background: rgba(26, 26, 46, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glow-text {
  text-shadow: 0 0 20px hsl(var(--neon-accent) / 0.5);
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes expandLine {
  0%,
  100% {
    transform: scaleX(0);
    opacity: 0;
  }
  50% {
    transform: scaleX(1);
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(60px);
  transition: all 0.8s ease-out;
}

.animate-on-scroll.visible {
  opacity: 1;
  transform: translateY(0);
}

.skill-bar {
  position: relative;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(
    90deg,
    hsl(var(--neon-accent)),
    hsl(var(--space-accent))
  );
  transition: width 2s ease-out;
  box-shadow: 0 0 10px hsl(var(--neon-accent) / 0.5);
  transform-origin: left;
  transform: scaleX(0);
}

.skill-progress.animate {
  transform: scaleX(1);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 30px hsl(var(--neon-accent) / 0.4);
  transform: translateY(-2px);
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 20px hsl(var(--neon-accent) / 0.5);
  }
  100% {
    box-shadow: 0 0 40px hsl(var(--neon-accent) / 0.8);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes particle-float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes infinite-scroll {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(
      -50%
    ); /* Move by half to create seamless loop with duplicated content */
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-infinite-scroll {
  animation: infinite-scroll linear infinite;
}

.animate-paused {
  animation-play-state: paused !important;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

.bg-grid-pattern {
  background-image: linear-gradient(
      to right,
      rgba(100, 100, 255, 0.1) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(100, 100, 255, 0.1) 1px, transparent 1px);
  background-size: 30px 30px;
}

/* Enhanced skill card hover effects */
.skill-card-hover {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.skill-card-hover::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(100, 100, 255, 0.05),
    transparent
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: inherit;
}

.skill-card-hover:hover::before {
  opacity: 1;
}

/* Skill badge pulse effect */
.skill-badge-pulse {
  position: relative;
  overflow: hidden;
}

.skill-badge-pulse::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(100, 100, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.skill-badge-pulse:hover::after {
  left: 100%;
}

.animate-paused {
  animation-play-state: paused !important;
}

.particle {
  position: fixed;
  width: 4px;
  height: 4px;
  background: hsl(var(--neon-accent));
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
  animation: particle-float 20s linear infinite;
}

/* Hero section blending effects */
.hero-blend-transition {
  position: relative;
  z-index: 5;
}

.hero-blend-transition::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, transparent, hsl(var(--space-dark)));
  pointer-events: none;
}

/* Responsive adjustments for blending effects */
@media (max-width: 768px) {
  .hero-blend-transition::after {
    height: 60px;
  }
}

@media (max-width: 480px) {
  .hero-blend-transition::after {
    height: 40px;
  }
}

/* Blending animation keyframes */
@keyframes waveFlow {
  0% {
    transform: translateY(0) scaleY(1) scaleX(1);
    opacity: 0.9;
  }
  25% {
    transform: translateY(3px) scaleY(1.1) scaleX(1.02);
    opacity: 1;
  }
  50% {
    transform: translateY(-4px) scaleY(0.9) scaleX(0.98);
    opacity: 0.7;
  }
  75% {
    transform: translateY(2px) scaleY(1.05) scaleX(1.01);
    opacity: 0.9;
  }
  100% {
    transform: translateY(0) scaleY(1) scaleX(1);
    opacity: 0.9;
  }
}

@keyframes waveFlowDelayed {
  0% {
    transform: translateY(0) scaleY(1) scaleX(1) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-2px) scaleY(0.95) scaleX(1.03) rotate(1deg);
    opacity: 0.9;
  }
  50% {
    transform: translateY(4px) scaleY(1.08) scaleX(0.97) rotate(-1deg);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-1px) scaleY(1.02) scaleX(1.01) rotate(0.5deg);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0) scaleY(1) scaleX(1) rotate(0deg);
    opacity: 0.8;
  }
}

@keyframes particleFlow {
  0%,
  100% {
    transform: translateX(0) translateY(0) scale(1) rotate(0deg);
    opacity: 0.9;
    filter: brightness(1) saturate(1);
  }
  25% {
    transform: translateX(4px) translateY(-3px) scale(1.3) rotate(90deg);
    opacity: 1;
    filter: brightness(1.5) saturate(1.5);
  }
  50% {
    transform: translateX(-3px) translateY(4px) scale(0.7) rotate(180deg);
    opacity: 0.6;
    filter: brightness(0.8) saturate(2);
  }
  75% {
    transform: translateX(-4px) translateY(-2px) scale(1.1) rotate(270deg);
    opacity: 0.9;
    filter: brightness(1.2) saturate(1.3);
  }
}

@keyframes gradientFlow {
  0% {
    background-position: -200% center;
    opacity: 0.6;
    filter: hue-rotate(0deg) brightness(1);
  }
  25% {
    background-position: -50% center;
    opacity: 0.9;
    filter: hue-rotate(90deg) brightness(1.3);
  }
  50% {
    background-position: 200% center;
    opacity: 1;
    filter: hue-rotate(180deg) brightness(1.5);
  }
  75% {
    background-position: 50% center;
    opacity: 0.8;
    filter: hue-rotate(270deg) brightness(1.2);
  }
  100% {
    background-position: -200% center;
    opacity: 0.6;
    filter: hue-rotate(360deg) brightness(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Animation classes */
.animate-wave-flow {
  animation: waveFlow 4s ease-in-out infinite;
}

.animate-wave-flow-delayed {
  animation: waveFlowDelayed 5s ease-in-out infinite;
  animation-delay: 1s;
}

.animate-particle-flow {
  animation: particleFlow 3s ease-in-out infinite;
}

.animate-gradient-flow {
  animation: gradientFlow 6s linear infinite;
}

/* Additional animation classes for blending effects */
.animate-spin-slow {
  animation: spin 8s linear infinite;
}

.animate-pulse-slow {
  animation: pulse 2s ease-in-out infinite;
}

.animate-twinkle {
  animation: twinkle 1.5s ease-in-out infinite;
}

/* Portal window effect for InfiniteVerticalSlider */
.portal-window {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(26, 26, 46, 0.95) 0%,
    rgba(26, 26, 46, 0.85) 50%,
    rgba(26, 26, 46, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.portal-window::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center,
    transparent 30%,
    rgba(26, 26, 46, 0.3) 70%,
    rgba(26, 26, 46, 0.8) 100%
  );
  pointer-events: none;
  z-index: 1;
}

/* Transition blend element for SVG animations */
.transition-blend-element {
  mix-blend-mode: screen;
  filter: blur(0.5px);
  opacity: 0.9;
}

.animate-float-slow {
  animation: float 4s ease-in-out infinite;
}

.animate-float-medium {
  animation: float 3s ease-in-out infinite;
  animation-delay: 1s;
}

.animate-float-fast {
  animation: float 2s ease-in-out infinite;
  animation-delay: 0.5s;
}

/* Reviews section simple blend transition effects */
.reviews-blend-container {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    180deg,
    hsl(var(--space-dark)) 0%,
    hsl(var(--space-primary) / 0.3) 50%,
    hsl(var(--space-dark)) 100%
  );
}

/* Top emergence effect - reviews fade in from background */
.reviews-blend-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120px;
  background: linear-gradient(
    to bottom,
    hsl(var(--space-dark)) 0%,
    hsl(var(--space-dark) / 0.9) 20%,
    hsl(var(--space-primary) / 0.6) 50%,
    hsl(var(--space-primary) / 0.3) 80%,
    transparent 100%
  );
  pointer-events: none;
  z-index: 10;
}

/* Bottom fade effect - reviews vanish back into background */
.reviews-blend-container::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px;
  background: linear-gradient(
    to top,
    hsl(var(--space-dark)) 0%,
    hsl(var(--space-dark) / 0.9) 20%,
    hsl(var(--space-primary) / 0.6) 50%,
    hsl(var(--space-primary) / 0.3) 80%,
    transparent 100%
  );
  pointer-events: none;
  z-index: 10;
}

/* Subtle side vignette for depth */
.reviews-blend-container .reviews-content {
  position: relative;
  mask-image: linear-gradient(
    to right,
    transparent 0%,
    black 5%,
    black 95%,
    transparent 100%
  );
  -webkit-mask-image: linear-gradient(
    to right,
    transparent 0%,
    black 5%,
    black 95%,
    transparent 100%
  );
}

/* Enhanced review item emergence animation */
.review-item-emerge {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.review-item-emerge.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Smooth scroll-based opacity transitions */
.review-item-fade {
  transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}

.review-item-fade.fading-out {
  opacity: 0.3;
  transform: scale(0.98);
}

/* Sophisticated Shadow System for Reviews */
.sophisticated-shadow-system {
  mix-blend-mode: multiply;
  filter: contrast(1.1) brightness(0.95);
}

.sophisticated-shadow-system-bottom {
  mix-blend-mode: multiply;
  filter: contrast(1.1) brightness(0.95);
}

/* Enhanced breathing effect for sophisticated shadows */
@keyframes sophisticatedBreathing {
  0%,
  100% {
    filter: contrast(1.1) brightness(0.95) saturate(1);
    transform: scale(1);
  }
  50% {
    filter: contrast(1.2) brightness(0.9) saturate(1.1);
    transform: scale(1.02);
  }
}

.sophisticated-shadow-system,
.sophisticated-shadow-system-bottom {
  animation: sophisticatedBreathing 8s ease-in-out infinite;
}

/* Responsive adjustments for reviews blend effects */
@media (max-width: 768px) {
  .reviews-blend-container::before,
  .reviews-blend-container::after {
    height: 80px;
  }

  .reviews-blend-container .reviews-content {
    mask-image: linear-gradient(
      to right,
      transparent 0%,
      black 8%,
      black 92%,
      transparent 100%
    );
    -webkit-mask-image: linear-gradient(
      to right,
      transparent 0%,
      black 8%,
      black 92%,
      transparent 100%
    );
  }
}

@media (max-width: 480px) {
  .reviews-blend-container::before,
  .reviews-blend-container::after {
    height: 60px;
  }

  .reviews-blend-container .reviews-content {
    mask-image: linear-gradient(
      to right,
      transparent 0%,
      black 10%,
      black 90%,
      transparent 100%
    );
    -webkit-mask-image: linear-gradient(
      to right,
      transparent 0%,
      black 10%,
      black 90%,
      transparent 100%
    );
  }

  .review-item-emerge {
    transform: translateY(15px) scale(0.96);
  }

  /* Reduce sophisticated shadow intensity on mobile */
  .sophisticated-shadow-system,
  .sophisticated-shadow-system-bottom {
    filter: contrast(1.05) brightness(0.97);
    animation-duration: 12s;
  }
}

@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .animate-glow,
  .animate-shimmer,
  .animate-wave-flow,
  .animate-wave-flow-delayed,
  .animate-particle-flow,
  .animate-gradient-flow,
  .animate-spin-slow,
  .animate-pulse-slow,
  .animate-twinkle,
  .animate-float-slow,
  .animate-float-medium,
  .animate-float-fast,
  .particle,
  .animate-on-scroll,
  .sophisticated-shadow-system,
  .sophisticated-shadow-system-bottom {
    animation: none !important;
    transition: none !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--space-primary));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--neon-accent));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--neon-accent) / 0.8);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
