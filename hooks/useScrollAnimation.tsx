import { useEffect, useRef, useState } from 'react';

/**
 * Custom hook for scroll-based animations with hydration safety
 * @param {number} threshold - Intersection threshold (0-1)
 * @returns {Object} ref and isVisible state
 */
export const useScrollAnimation = (threshold = 0.1) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  // Ensure we're on the client side before using IntersectionObserver
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    // Only run on client side after mounting
    if (!isMounted || typeof window === 'undefined') {
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      {
        threshold,
        rootMargin: '0px 0px -50px 0px',
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [threshold, isMounted]);

  return { ref, isVisible: isMounted ? isVisible : false };
};
