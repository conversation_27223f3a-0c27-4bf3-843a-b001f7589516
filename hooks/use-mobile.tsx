import * as React from "react"

const MO<PERSON>LE_BREAKPOINT = 768

/**
 * Custom hook to detect mobile devices with hydration safety
 * Returns false during SSR and proper value after client hydration
 * @returns {boolean} Whether the current viewport is mobile-sized
 */
export const useIsMobile = () => {
  const [isMobile, setIsMobile] = React.useState<boolean>(false)
  const [isMounted, setIsMounted] = React.useState(false)

  React.useEffect(() => {
    setIsMounted(true)
  }, [])

  React.useEffect(() => {
    if (!isMounted || typeof window === 'undefined') {
      return
    }

    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MO<PERSON>LE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [isMounted])

  // Return false during SSR to prevent hydration mismatch
  return isMounted ? isMobile : false
}
